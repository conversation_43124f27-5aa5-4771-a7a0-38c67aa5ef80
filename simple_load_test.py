#!/usr/bin/env python3
"""
Simple Load Test for EventEase - Direct Python approach
"""

import requests
import time
import json
import threading
import random
import string
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics

class SimpleLoadTester:
    def __init__(self, base_url='http://127.0.0.1:5001'):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = []
        self.errors = []
        
    def create_test_user(self):
        """Create a test user"""
        user_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return {
            'email': f'test_{user_id}@example.com',
            'password': 'TestPassword123!',
            'role': random.choice(['student', 'teacher'])
        }
    
    def register_and_login(self):
        """Register and login a test user"""
        user_data = self.create_test_user()
        
        # Register
        try:
            register_response = self.session.post(
                f'{self.base_url}/register',
                json=user_data,
                timeout=10
            )
            
            # Login
            login_response = self.session.post(
                f'{self.base_url}/login',
                json={
                    'email': user_data['email'],
                    'password': user_data['password']
                },
                timeout=10
            )
            
            if login_response.status_code == 200:
                return True, user_data
            else:
                return False, f"Login failed: {login_response.status_code}"
                
        except Exception as e:
            return False, f"Auth error: {str(e)}"
    
    def test_endpoint(self, method, endpoint, data=None, expected_status=200):
        """Test a single endpoint"""
        start_time = time.time()
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(f'{self.base_url}{endpoint}', timeout=10)
            elif method.upper() == 'POST':
                response = self.session.post(f'{self.base_url}{endpoint}', json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to ms
            
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': response.status_code,
                'response_time': response_time,
                'success': response.status_code == expected_status,
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': 0,
                'response_time': response_time,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            return result
    
    def user_simulation(self, duration_seconds=60):
        """Simulate a single user's behavior"""
        end_time = time.time() + duration_seconds
        user_results = []
        
        # Login first
        login_success, login_data = self.register_and_login()
        if not login_success:
            return [{'error': f'Login failed: {login_data}', 'success': False}]
        
        while time.time() < end_time:
            # Simulate user actions
            actions = [
                ('GET', '/dashboard', None, 200),
                ('GET', '/api/events', None, 200),
                ('POST', '/event/create', {
                    'title': 'Test Event',
                    'description': 'Test Description',
                    'start_time': '2024-01-15T10:00',
                    'end_time': '2024-01-15T11:00',
                    'priority': 1,
                    'event_type': 'single'
                }, 200),
                ('GET', '/profile/info', None, 200),
                ('POST', '/api/profile/tags', {'tags': 'test, load'}, 200)
            ]
            
            # Pick a random action
            method, endpoint, data, expected_status = random.choice(actions)
            result = self.test_endpoint(method, endpoint, data, expected_status)
            user_results.append(result)
            
            # Wait between requests
            time.sleep(random.uniform(0.5, 2.0))
        
        return user_results
    
    def run_load_test(self, num_users=10, duration_seconds=60):
        """Run load test with multiple concurrent users"""
        print(f"🚀 Starting load test with {num_users} users for {duration_seconds} seconds")
        print(f"Target: {self.base_url}")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_users) as executor:
            # Submit user simulations
            futures = [
                executor.submit(self.user_simulation, duration_seconds)
                for _ in range(num_users)
            ]
            
            # Collect results
            all_results = []
            for future in as_completed(futures):
                try:
                    user_results = future.result()
                    all_results.extend(user_results)
                except Exception as e:
                    print(f"User simulation error: {e}")
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Analyze results
        self.analyze_results(all_results, total_duration)
        
        return all_results
    
    def analyze_results(self, results, total_duration):
        """Analyze and print test results"""
        if not results:
            print("❌ No results to analyze")
            return
        
        # Filter out error results for metrics
        successful_results = [r for r in results if r.get('success', False)]
        failed_results = [r for r in results if not r.get('success', False)]
        
        total_requests = len(results)
        successful_requests = len(successful_results)
        failed_requests = len(failed_results)
        
        if successful_results:
            response_times = [r['response_time'] for r in successful_results]
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else max_response_time
        else:
            avg_response_time = max_response_time = min_response_time = p95_response_time = 0
        
        requests_per_second = total_requests / total_duration if total_duration > 0 else 0
        success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
        
        # Print results
        print("\n" + "="*60)
        print("📊 LOAD TEST RESULTS")
        print("="*60)
        print(f"Duration: {total_duration:.1f} seconds")
        print(f"Total Requests: {total_requests:,}")
        print(f"Successful Requests: {successful_requests:,}")
        print(f"Failed Requests: {failed_requests:,}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Requests/Second: {requests_per_second:.1f}")
        print(f"Avg Response Time: {avg_response_time:.1f}ms")
        print(f"Min Response Time: {min_response_time:.1f}ms")
        print(f"Max Response Time: {max_response_time:.1f}ms")
        print(f"95th Percentile: {p95_response_time:.1f}ms")
        
        # Performance assessment
        print("\n🎯 PERFORMANCE ASSESSMENT:")
        
        if success_rate >= 95:
            print("✅ Success Rate: EXCELLENT")
        elif success_rate >= 90:
            print("⚠️  Success Rate: GOOD")
        else:
            print("❌ Success Rate: POOR")
        
        if avg_response_time <= 500:
            print("✅ Response Time: EXCELLENT")
        elif avg_response_time <= 1000:
            print("⚠️  Response Time: GOOD")
        else:
            print("❌ Response Time: POOR")
        
        if requests_per_second >= 10:
            print("✅ Throughput: EXCELLENT")
        elif requests_per_second >= 5:
            print("⚠️  Throughput: GOOD")
        else:
            print("❌ Throughput: POOR")
        
        # Show error summary
        if failed_results:
            print(f"\n❌ ERROR SUMMARY ({failed_requests} failures):")
            error_counts = {}
            for result in failed_results:
                error_key = result.get('error', f"HTTP {result.get('status_code', 'Unknown')}")
                error_counts[error_key] = error_counts.get(error_key, 0) + 1
            
            for error, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"   {error}: {count} times")
        
        print("="*60)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Simple Load Tester for EventEase')
    parser.add_argument('--host', default='http://127.0.0.1:5001', help='Target host URL')
    parser.add_argument('--users', type=int, default=10, help='Number of concurrent users')
    parser.add_argument('--duration', type=int, default=60, help='Test duration in seconds')
    
    args = parser.parse_args()
    
    tester = SimpleLoadTester(args.host)
    results = tester.run_load_test(args.users, args.duration)
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'simple_load_test_results_{timestamp}.json'
    
    with open(results_file, 'w') as f:
        json.dump({
            'test_config': {
                'host': args.host,
                'users': args.users,
                'duration': args.duration,
                'timestamp': timestamp
            },
            'results': results
        }, f, indent=2)
    
    print(f"\n💾 Results saved to: {results_file}")

if __name__ == "__main__":
    main()
