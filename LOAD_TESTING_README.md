# EventEase Load Testing Suite

Comprehensive load testing setup for the EventEase Flask application using Locust.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r load_test_requirements.txt
```

### 2. Start Your Application
Make sure your EventEase application is running:
```bash
python app.py
```

### 3. Run Load Tests

#### Option A: Run All Test Scenarios
```bash
python run_load_tests.py
```

#### Option B: Run Specific Scenario
```bash
python run_load_tests.py --scenario light_load
```

#### Option C: Run Locust Directly (with Web UI)
```bash
locust -f locustfile.py --host=http://127.0.0.1:5001
```
Then open http://localhost:8089 in your browser.

## 📊 Test Scenarios

### 1. Light Load (`light_load`)
- **Users**: 10 concurrent users
- **Spawn Rate**: 2 users/second
- **Duration**: 2 minutes
- **Purpose**: Basic functionality testing

### 2. Normal Load (`normal_load`)
- **Users**: 50 concurrent users
- **Spawn Rate**: 5 users/second
- **Duration**: 5 minutes
- **Purpose**: Typical usage simulation

### 3. Stress Test (`stress_test`)
- **Users**: 100 concurrent users
- **Spawn Rate**: 10 users/second
- **Duration**: 10 minutes
- **Purpose**: High load testing

### 4. Spike Test (`spike_test`)
- **Users**: 200 concurrent users
- **Spawn Rate**: 50 users/second
- **Duration**: 3 minutes
- **Purpose**: Sudden traffic spike simulation

## 🎯 What Gets Tested

### Core Functionality
- **Authentication**: Login, registration, logout
- **Dashboard**: Main dashboard loading and navigation
- **Events**: Creation, retrieval, updates, deletion
- **Profile Management**: Profile pages, settings updates
- **AI Features**: Event summarization
- **Notifications**: Notification system

### Security Testing
- **XSS Prevention**: Cross-site scripting attack attempts
- **SQL Injection**: Database injection attack attempts
- **Input Validation**: Malicious input handling
- **Error Handling**: Proper error responses

### Performance Metrics
- **Response Times**: Average, maximum, percentiles
- **Throughput**: Requests per second
- **Success Rates**: Percentage of successful requests
- **Failure Analysis**: Types and causes of failures
- **Resource Usage**: Memory, CPU, database connections

## 📈 Performance Thresholds

The tests check against these performance thresholds:
- **Max Response Time**: 2000ms (2 seconds)
- **Max Failure Rate**: 5%
- **Min Requests/Second**: 10 RPS

## 📁 Test Results

Results are saved in timestamped directories:
```
load_test_results_YYYYMMDD_HHMMSS/
├── light_load_report.html          # HTML report
├── light_load_stats.csv            # Request statistics
├── light_load_failures.csv         # Failure details
├── light_load_results.json         # Parsed results
├── normal_load_report.html
├── stress_test_report.html
├── spike_test_report.html
└── final_report.json               # Overall summary
```

## 🔧 Customization

### Modify Test Scenarios
Edit `locust_config.py` to adjust:
- User counts and spawn rates
- Test duration
- Performance thresholds
- Security test payloads

### Add Custom Tests
In `locustfile.py`, add new test methods:
```python
@task(1)
def my_custom_test(self):
    with self.client.get('/my-endpoint', catch_response=True) as response:
        if response.status_code == 200:
            response.success()
        else:
            response.failure(f"Failed: {response.status_code}")
```

### Adjust User Behavior
Modify task weights in the `@task(weight)` decorators:
- Higher weight = more frequent execution
- Lower weight = less frequent execution

## 🛡️ Security Testing

The suite includes specialized security tests:

### XSS Testing
Tests various XSS payloads in:
- Event titles and descriptions
- Profile information
- Form inputs

### SQL Injection Testing
Tests SQL injection attempts in:
- Login forms
- Search parameters
- User inputs

### Input Validation Testing
Verifies proper handling of:
- Malformed JSON
- Oversized payloads
- Invalid data types
- Missing required fields

## 📊 Interpreting Results

### Success Indicators
- ✅ Response times under 2 seconds
- ✅ Failure rate under 5%
- ✅ No 500 errors from security tests
- ✅ Consistent throughput

### Warning Signs
- ⚠️ Increasing response times over test duration
- ⚠️ High failure rates (>5%)
- ⚠️ Memory leaks or resource exhaustion
- ⚠️ Database connection issues

### Critical Issues
- ❌ Application crashes or timeouts
- ❌ Data corruption or loss
- ❌ Security vulnerabilities exposed
- ❌ Complete service unavailability

## 🔍 Monitoring During Tests

### Application Monitoring
Monitor your application for:
- CPU usage
- Memory consumption
- Database connections
- Response times
- Error logs

### System Resources
Watch system metrics:
```bash
# Monitor system resources
htop

# Monitor database
sqlite3 app.db ".tables"

# Monitor application logs
tail -f app.log
```

## 🚨 Troubleshooting

### Common Issues

#### "Connection refused" errors
- Ensure your application is running on the correct port
- Check firewall settings
- Verify the host URL in the test configuration

#### High failure rates
- Check application logs for errors
- Verify database connectivity
- Ensure sufficient system resources

#### Slow response times
- Check database query performance
- Monitor system resource usage
- Consider database indexing

### Debug Mode
Run with verbose output:
```bash
locust -f locustfile.py --host=http://127.0.0.1:5001 --loglevel DEBUG
```

## 📝 Best Practices

1. **Start Small**: Begin with light load tests before stress testing
2. **Monitor Resources**: Watch system resources during tests
3. **Baseline First**: Establish performance baselines
4. **Test Regularly**: Include load testing in your CI/CD pipeline
5. **Document Results**: Keep records of test results over time
6. **Fix Issues**: Address performance issues before they become critical

## 🔄 Continuous Integration

To integrate with CI/CD:
```bash
# Run headless tests with exit codes
python run_load_tests.py --scenario normal_load
if [ $? -eq 0 ]; then
    echo "Load tests passed"
else
    echo "Load tests failed"
    exit 1
fi
```

## 📞 Support

For issues with the load testing suite:
1. Check the application logs
2. Verify all dependencies are installed
3. Ensure the application is running and accessible
4. Review the test configuration in `locust_config.py`

Happy load testing! 🚀
