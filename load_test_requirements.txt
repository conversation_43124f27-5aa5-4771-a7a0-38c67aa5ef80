# Load Testing Requirements for EventEase
# Install with: pip install -r load_test_requirements.txt

# Core load testing framework
locust>=2.17.0

# Additional utilities for enhanced testing
requests>=2.31.0
psutil>=5.9.0  # For system resource monitoring
matplotlib>=3.7.0  # For generating charts
pandas>=2.0.0  # For data analysis
numpy>=1.24.0  # For statistical calculations

# Reporting and visualization
jinja2>=3.1.0  # For custom report templates
plotly>=5.15.0  # For interactive charts

# Security testing utilities
faker>=19.0.0  # For generating test data
python-dateutil>=2.8.0  # For date manipulation

# Optional: Database monitoring (if needed)
sqlalchemy>=2.0.0  # For database connection monitoring
pymongo>=4.5.0  # For MongoDB monitoring (if used)

# Development and debugging
colorama>=0.4.6  # For colored terminal output
tqdm>=4.66.0  # For progress bars
