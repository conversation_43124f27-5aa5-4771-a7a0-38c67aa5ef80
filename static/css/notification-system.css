/*===== CUSTOM NOTIFICATION SYSTEM STYLES =====*/
/* Toast-style notifications for user feedback (success, error, info) */

/* Container for all notifications - positioned in top-right corner */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;        /* High z-index to appear above all content */
  display: flex;
  flex-direction: column; /* Stack notifications vertically */
  gap: 10px;
  pointer-events: none;   /* Allow clicks to pass through container */
}

/* Individual notification styling */
.notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  max-width: 400px;
  opacity: 0;                          /* Hidden by default */
  transform: translateX(100%);         /* Start off-screen to the right */
  transition: all 0.3s ease-in-out;   /* Smooth slide-in animation */
  pointer-events: auto;                /* Allow interaction with notification */
  border-left: 4px solid;             /* Colored border (color set by type) */
}

/* Show state - notification slides in and becomes visible */
.notification-show {
  opacity: 1;
  transform: translateX(0);  /* Slide to final position */
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.notification-content i:first-child {
  font-size: 18px;
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
}

.notification-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.notification-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

/*===== NOTIFICATION TYPES =====*/

.notification-success {
  border-left-color: #4caf50;
}

.notification-success .notification-content i:first-child {
  color: #4caf50;
}

.notification-error {
  border-left-color: #f44336;
}

.notification-error .notification-content i:first-child {
  color: #f44336;
}

.notification-warning {
  border-left-color: #ff9800;
}

.notification-warning .notification-content i:first-child {
  color: #ff9800;
}

.notification-info {
  border-left-color: #2196f3;
}

.notification-info .notification-content i:first-child {
  color: #2196f3;
}

/*===== RESPONSIVE DESIGN =====*/

@media (max-width: 768px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .notification {
    min-width: auto;
    max-width: none;
  }
}
