html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  overflow: hidden;
}

/* ===== SIDEBAR ===== */
.sidebar {
  width: 220px;
  background-color: #2c3e50;
  padding: 20px;
  box-sizing: border-box;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.sidebar h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 1.5rem;
  text-align: center;
}

.sidebar .tab {
  display: block;
  color: #ecf0f1;
  padding: 12px 15px;
  margin-bottom: 10px;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  text-align: center;
}

.sidebar .tab:hover,
.sidebar .tab.active {
  background-color: #34495e;
  color: #fff;
}

/* ===== MAIN CONTENT ===== */
.main-content {
  margin-left: 220px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ===== NAVBAR ===== */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
  height: 64px;
  box-sizing: border-box;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-left .create-button {
  background-color: #1a73e8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
}

.nav-left .create-button:hover {
  background-color: #1765c1;
}

.nav-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-grow: 1;
  position: relative;
  user-select: none;
  font-weight: 600;
  font-size: 1.5rem; /* Reduced size for better spacing */
  color: #333;
}

.date-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px; /* Adjusted for consistent spacing */
  position: relative;
}

#calendarView {
  width: 110px;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #fff;
  font-size: 0.95rem;
  cursor: pointer;
  margin-right: 20px; /* Positioned to the left of the prevMonth button */
}

#prevMonth, #nextMonth {
  width: 36px;
  height: 36px;
  font-size: 1.4rem;
  border-radius: 50%;
  border: none;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  user-select: none;
}

#prevMonth:hover, #nextMonth:hover {
  background-color: #0056b3;
}

.date-display {
  pointer-events: none;
  white-space: nowrap;
  margin: 0 20px; /* Adjusted margin for better spacing */
  min-width: 150px; /* Ensure consistent width for longer month names */
  text-align: center;
}

/* ===== USER MENU ===== */
.user-menu {
  position: relative;
  display: inline-block;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 10px;
}

.user-profile .avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.user-profile .username {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.9rem;
}

.user-profile .arrow.down::after {
  content: "▼";
  font-size: 0.6rem;
  margin-left: 5px;
  color: #666;
}

.menu-dropdown {
  display: none;
  position: absolute;
  right: 0;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;
  z-index: 1000;
  min-width: 150px;
}

.menu-dropdown a,
.menu-dropdown button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  text-align: left;
  background: none;
  border: none;
  outline: none;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
}

.menu-dropdown a:hover,
.menu-dropdown button:hover {
  background-color: #f0f0f0;
}

.menu-dropdown.show {
  display: block;
}

/* ===== CALENDAR GRID ===== */
.calendar-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  box-sizing: border-box;
  overflow: hidden;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: auto repeat(5, 1fr);
  gap: 0.5rem;
  flex-grow: 1;
  box-sizing: border-box;
}

.day-header {
  font-weight: bold;
  text-align: center;
  padding: 0.5rem 0;
  background-color: #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== DAY CELL ===== */
.day-cell {
  height: 115px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  overflow-y: auto;
}

.day-cell:hover {
  background-color: #f1f3f5;
  box-shadow: 0 0 0 2px #007bff inset;
}

.add-event-btn {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  text-align: center;
  line-height: 22px;
  font-size: 16px;
  padding: 0;
  display: none;
  z-index: 10;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.day-cell:hover .add-event-btn {
  display: block;
}

.day-cell .event-item {
  margin-bottom: 2px;
  max-width: calc(100% - 10px);
}

/* ===== EVENT STYLING ===== */
.event-item {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
  padding: 2px 4px;
  margin: 1px 0;
  font-size: 0.75rem;
  border-radius: 2px;
  cursor: pointer;
}

.event-item:hover {
  background-color: #bbdefb;
}

.event-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-time {
  color: #666;
  font-size: 0.7rem;
}

/* ===== PRIORITY COLORS ===== */
.priority-0 {
  border-left-color: #4caf50;
  background-color: #e8f5e8;
}

.priority-1 {
  border-left-color: #ff9800;
  background-color: #fff3e0;
}

.priority-2 {
  border-left-color: #f44336;
  background-color: #ffebee;
}

/* ===== RESPONSIVENESS ===== */
@media (max-width: 1200px) {
  .sidebar {
    width: 180px;
  }
  .main-content {
    margin-left: 180px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 60px;
    padding: 10px;
  }
  .sidebar h2 {
    font-size: 1rem;
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
  .sidebar .tab {
    padding: 8px 5px;
    font-size: 0.8rem;
  }
  .main-content {
    margin-left: 60px;
  }
  .navbar {
    padding: 0.5rem 1rem;
  }
  .user-profile .username {
    max-width: 120px;
    font-size: 0.8rem;
  }
}

/* ===== CALENDAR VIEW TYPE ===== */
#calendarContainer {
  padding: 15px 0 0 0;
  height: auto;
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  width: 100%;
}

/*===== COMMON DAY AND WEEK GRID STYLES =====*/
.timeline-grid, .week-grid {
  display: grid;
  width: 100%;
  border: 1px solid #ddd;
  height: fit-content;
  position: relative;
  overflow-x: hidden;
}

/*===== FIXED GRID LAYOUTS =====*/
.timeline-grid {
  grid-template-columns: 60px 1fr;
  width: 100%;
  max-width: 100%;
}

.week-grid {
  grid-template-columns: 60px repeat(7, 1fr);
  width: 100%;
  max-width: 100%;
}

/* Common styles for headers */
.grid-header {
  height: 40px;
  padding: 5px;
  text-align: center;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  position: sticky;
  top: 0;
  z-index: 20;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-header {
  border-right: 1px solid #ddd;
}

.day-header-label.today {
  background-color: #e3f2fd;
  color: #1976d2;
}

.day-name {
  font-size: 0.8rem;
}

.day-number {
  font-size: 1rem;
}

/* Common styles for time column */
.time-column {
  background-color: #f9f9f9;
  border-right: 1px solid #ddd;
}

.hour-label {
  height: 60px;
  padding: 5px 8px 5px 5px;
  text-align: right;
  border-bottom: 1px solid #ddd;
  font-size: 0.8rem;
  color: #666;
  box-sizing: border-box;
}

/* Day column styles */
.day-column {
  position: relative;
  border-right: 1px solid #eee;
}

.day-column:last-child {
  border-right: none;
}

/* Hour cells */
.hours-container {
  position: relative;
}

.hour-cell {
  height: 60px;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;
}

.hour-cell-current {
  background-color: #fffde7;
}

/* Events container */
.events-container {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* Event styling for day/week view */
.events-container .event-item {
  position: absolute;
  left: 4px;
  right: 4px;
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
  border-radius: 4px;
  padding: 4px;
  font-size: 0.8rem;
  overflow: hidden;
  pointer-events: auto;
  z-index: 5;
}

/* Current time indicator */
.current-time-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e53935;
  z-index: 100;
  pointer-events: none;
}

/* Make day headers in week view clickable */
.week-grid .day-header-label {
  cursor: pointer;
  transition: background-color 0.2s;
}

.week-grid .day-header-label:hover {
  background-color: #e3f2fd;
}

/* ===== FIXED NAVIGATION POSITIONING ===== */
.calendar-controls {
  position: relative;
  height: 44px;
  margin: 1rem 0;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

#calendarView {
  width: 110px;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #fff;
  font-size: 0.95rem;
  cursor: pointer;
}

#prevMonth, #nextMonth {
  width: 36px;
  height: 36px;
  font-size: 1.4rem;
  border-radius: 50%;
  border: none;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  user-select: none;
}

#prevMonth:hover, #nextMonth:hover {
  background-color: #0056b3;
}

.date-display {
  pointer-events: none;
  white-space: nowrap;
  margin: 0 20px; /* Adjusted margin for better spacing */
  min-width: 150px; /* Ensure consistent width for longer month names */
  text-align: center;
}

/* ===== EVENT MODAL ===== */
.event-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
}

.event-modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 500px;
  max-width: 90%;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #666;
  cursor: pointer;
}

.close-modal:hover {
  color: #333;
}

.event-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.event-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.event-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-right: 25px;
}

.event-badge.low {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.event-badge.medium {
  background-color: #fff8e1;
  color: #f57c00;
}

.event-badge.high {
  background-color: #ffebee;
  color: #c62828;
}

.event-modal-body {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.event-time-info,
.event-recurrence-info,
.event-description-info {
  display: flex;
  gap: 10px;
}

.event-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  color: #666;
}

.event-time-details {
  display: flex;
  flex-direction: column;
}

#eventDate {
  font-weight: 500;
}

#eventTime {
  color: #666;
}

.event-recurrence-info {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

#eventDescription {
  line-height: 1.5;
  color: #333;
}

.event-metadata {
  margin-top: 10px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.event-genre {
  font-weight: 500;
  color: #555;
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag {
  background-color: #e0e0e0;
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.event-modal-footer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn {
  padding: 8px 15px;
  border-radius: 5px;
  border: none;
  font-weight: 600;
  cursor: pointer;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #ddd;
  color: #333;
}

.btn-secondary:hover {
  background-color: #bbb;
}

/* ===== NOTIFICATIONS ===== */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 280px;
  max-width: 100vw;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  background-color: #007bff;
  color: white;
  padding: 12px 18px;
  border-radius: 6px;
  box-shadow: 0 4px 10px rgba(0,0,0,0.2);
  opacity: 0.95;
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: slideInRight 0.3s ease forwards;
}

.notification-error {
  background-color: #f44336;
}

.notification-success {
  background-color: #4caf50;
}

.notification-close {
  cursor: pointer;
  margin-left: 10px;
  font-weight: bold;
  font-size: 1.1rem;
  user-select: none;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 0.95;
  }
}
