body {
margin: 0;
font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
background-color: #f8f9fa;
}

.navbar {
display: flex;
justify-content: space-between;
align-items: center;
padding: 0.75rem 1.5rem;
background-color: #ffffff;
border-bottom: 1px solid #ddd;
position: sticky;
top: 0;
z-index: 1000;
}

.nav-left .create-button {
background-color: #1a73e8;
color: white;
border: none;
padding: 0.5rem 1rem;
font-weight: bold;
border-radius: 4px;
cursor: pointer;
}

.nav-left .create-button:hover {
background-color: #1765c1;
}

.nav-center h1 {
margin: 0;
font-size: 1.5rem;
color: #333;
}

.nav-right .profile {
display: flex;
align-items: center;
gap: 0.5rem;
}

.nav-right .profile img {
width: 32px;
height: 32px;
border-radius: 50%;
object-fit: cover;
}

.nav-right .username {
font-weight: 500;
color: #444;
}

.calendar-container {
padding: 1.5rem;
}

.calendar-grid {
display: grid;
grid-template-columns: repeat(7, 1fr);
gap: 0.5rem;
}

.day-header {
font-weight: bold;
text-align: center;
padding: 0.5rem 0;
background-color: #e9ecef;
border-radius: 4px;
}

.day-cell {
background-color: #fff;
border: 1px solid #ddd;
min-height: 100px;
border-radius: 4px;
padding: 0.5rem;
position: relative;
}

.day-cell:hover {
background-color: #f1f3f5;
}

.day-number {
font-size: 0.85rem;
font-weight: bold;
color: #555;
}

/* Sidebar styles */
.sidebar {
width: 250px;
height: 100vh;
background-color: #2c3e50;
color: white;
position: fixed;
top: 0;
left: 0;
display: flex;
flex-direction: column;
padding: 20px;
}

.sidebar h2 {
margin: 0 0 20px;
font-size: 1.5rem;
text-align: center;
}

.sidebar .tab {
padding: 10px 15px;
margin: 5px 0;
background-color: #34495e;
border-radius: 5px;
text-align: center;
cursor: pointer;
transition: background-color 0.3s;
}

.sidebar .tab:hover {
background-color: #1abc9c;
}

/* Adjust main content to account for the sidebar */
.main-content {
margin-left: 250px;
padding: 20px;
}

/* Placeholder buttons */

#logout {
position: fixed;
bottom: 20px;
left: 20px;
font-size: 1.2rem;
padding: 12px 24px;
background-color: #dc3545; /* Bootstrap-style red for logout */
color: white;
border: none;
border-radius: 8px;
cursor: pointer;
z-index: 1000; /* stays above other elements */
box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
transition: background-color 0.2s ease;
  }
  
#logout:hover {
background-color: #0056b3;   /* Darker blue on hover */
  }