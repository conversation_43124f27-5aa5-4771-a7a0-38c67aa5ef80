body {
margin: 0;
font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
background-color: #f8f9fa;
}

.navbar {
display: flex;
justify-content: space-between;
align-items: center;
padding: 0.75rem 1.5rem;
background-color: #ffffff;
border-bottom: 1px solid #ddd;
position: sticky;
top: 0;
z-index: 1000;
}

.nav-left .create-button {
background-color: #1a73e8;
color: white;
border: none;
padding: 0.5rem 1rem;
font-weight: bold;
border-radius: 4px;
cursor: pointer;
}

.nav-left .create-button:hover {
background-color: #1765c1;
}

.nav-center h1 {
margin: 0;
font-size: 1.5rem;
color: #333;
}

.nav-right .profile {
display: flex;
align-items: center;
gap: 0.5rem;
}

.nav-right .profile img {
width: 32px;
height: 32px;
border-radius: 50%;
object-fit: cover;
}

.nav-right .username {
font-weight: 500;
color: #444;
}

.calendar-container {
padding: 1.5rem;
}

.calendar-grid {
display: grid;
grid-template-columns: repeat(7, 1fr);
gap: 0.5rem;
}

.day-header {
font-weight: bold;
text-align: center;
padding: 0.5rem 0;
background-color: #e9ecef;
border-radius: 4px;
}

.day-cell {
background-color: #fff;
border: 1px solid #ddd;
min-height: 100px;
border-radius: 4px;
padding: 0.5rem;
position: relative;
}

.day-cell:hover {
background-color: #f1f3f5;
}

.day-number {
font-size: 0.85rem;
font-weight: bold;
color: #555;
}

/* Sidebar styles */
.sidebar {
width: 250px;
height: 100vh;
background-color: #2c3e50;
color: white;
position: fixed;
top: 0;
left: 0;
display: flex;
flex-direction: column;
padding: 20px;
}

.sidebar h2 {
margin: 0 0 20px;
font-size: 1.5rem;
text-align: center;
}

.sidebar .tab {
padding: 10px 15px;
margin: 5px 0;
background-color: #34495e;
border-radius: 5px;
text-align: center;
cursor: pointer;
transition: background-color 0.3s;
}

.sidebar .tab:hover {
background-color: #1abc9c;
}

/* Adjust main content to account for the sidebar */
.main-content {
margin-left: 250px;
padding: 20px;
}

/* Event styling */
.event-item {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
  padding: 2px 4px;
  margin: 1px 0;
  font-size: 0.75rem;
  border-radius: 2px;
  cursor: pointer;
}

.event-item:hover {
  background-color: #bbdefb;
}

.event-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-time {
  color: #666;
  font-size: 0.7rem;
}

/* Priority-based styling */
.priority-0 {
  border-left-color: #4caf50; /* Green for low priority */
  background-color: #e8f5e8;
}

.priority-1 {
  border-left-color: #ff9800; /* Orange for medium priority */
  background-color: #fff3e0;
}

.priority-2 {
  border-left-color: #f44336; /* Red for high priority */
  background-color: #ffebee;
}

.calendar-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.calendar-controls h2 {
  margin: 0;
  font-size: 1.5rem;
}

.calendar-controls button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  font-size: 1rem;
  cursor: pointer;
  border-radius: 5px;
}

.add-event-btn {
  background-color: #007bff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  text-align: center;
  line-height: 22px;
  font-size: 16px;
  padding: 0;
}

.day-cell {
  position: relative;
  border: 1px solid #ccc;
  padding: 5px;
  height: 120px;
}

.day-cell:hover .add-event-btn {
  display: block;
}

.user-menu {
  position: relative;
  display: inline-block;
  font-family: sans-serif;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 10px;
}

.user-profile .avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.user-profile .arrow.down::after {
  content: "▼";
  font-size: 0.6rem;
  margin-left: 5px;
  color: #666;
}

.menu-dropdown {
  display: none;
  position: absolute;
  right: 0;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;
  z-index: 1000;
  min-width: 150px;
}

.menu-dropdown a,
.menu-dropdown button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  text-align: left;
  background: none;
  border: none;
  outline: none;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
}

.menu-dropdown a:hover,
.menu-dropdown button:hover {
  background-color: #f0f0f0;
}

/* Show menu when .show class is added */
.menu-dropdown.show {
  display: block;
}
