html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  overflow: hidden;
}

/* ===== SIDEBAR ===== */
.sidebar {
  width: 220px;
  background-color: #2c3e50;
  padding: 20px;
  box-sizing: border-box;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.sidebar h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 1.5rem;
  text-align: center;
}

.sidebar .tab {
  display: block;
  color: #ecf0f1;
  padding: 12px 15px;
  margin-bottom: 10px;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  text-align: center;
}

.sidebar .tab:hover,
.sidebar .tab.active {
  background-color: #34495e;
  color: #fff;
}

/* ===== MAIN CONTENT ===== */
.main-content {
  margin-left: 220px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ===== NAVBAR ===== */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
  height: 64px;
  box-sizing: border-box;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  /* --- FIX: Prevent this element from shrinking --- */
  flex-shrink: 0;
}

.nav-left .create-button {
  background-color: #1a73e8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
}

.nav-left .create-button:hover {
  background-color: #1765c1;
}

.nav-center {
  display: flex;
  justify-content: center;
  flex-grow: 1;
}

/* ===== USER MENU ===== */
.user-menu {
  position: relative;
  display: inline-block;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 10px;
}

.user-profile .avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.user-profile .username {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.9rem;
}

.user-profile .arrow.down::after {
  content: "▼";
  font-size: 0.6rem;
  margin-left: 5px;
  color: #666;
}

.menu-dropdown {
  display: none;
  position: absolute;
  right: 0;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;
  z-index: 1000;
  min-width: 150px;
}

.menu-dropdown a,
.menu-dropdown button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  text-align: left;
  background: none;
  border: none;
  outline: none;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
}

.menu-dropdown a:hover,
.menu-dropdown button:hover {
  background-color: #f0f0f0;
}

.menu-dropdown.show {
  display: block;
}

/* ===== CALENDAR GRID ===== */
.calendar-container {
  /* --- FIX: This element will now grow to fill the available space --- */
  flex: 1; 
  display: flex;
  flex-direction: column;
  padding: 1rem;
  box-sizing: border-box;
  overflow: hidden; /* Hide any overflow from the container itself */
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: auto repeat(5, 1fr); /* Changed to 5 rows for better fit, can be 6 */
  gap: 0.5rem;
  /* --- FIX: Remove fixed height and let it grow --- */
  flex-grow: 1;
  box-sizing: border-box;
}

.day-header {
  font-weight: bold;
  text-align: center;
  padding: 0.5rem 0;
  background-color: #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== DAY CELL ===== */
.day-cell {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  /* --- FIX: Allow scrolling within a cell if it's full --- */
  overflow-y: auto;
}

.day-cell:hover {
  background-color: #f1f3f5;
  box-shadow: 0 0 0 2px #007bff inset;
}

/* Position the add button in the bottom right corner */
.add-event-btn {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  text-align: center;
  line-height: 22px;
  font-size: 16px;
  padding: 0;
  display: none;
  z-index: 10;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.day-cell:hover .add-event-btn {
  display: block;
}

/* Make sure events don't overlap with the add button */
.day-cell .event-item {
  margin-bottom: 2px;
  max-width: calc(100% - 10px);
}

/* ===== EVENT STYLING ===== */
.event-item {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
  padding: 2px 4px;
  margin: 1px 0;
  font-size: 0.75rem;
  border-radius: 2px;
  cursor: pointer;
}

.event-item:hover {
  background-color: #bbdefb;
}

.event-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-time {
  color: #666;
  font-size: 0.7rem;
}

/* ===== PRIORITY COLORS ===== */
.priority-0 {
  border-left-color: #4caf50;
  background-color: #e8f5e8;
}

.priority-1 {
  border-left-color: #ff9800;
  background-color: #fff3e0;
}

.priority-2 {
  border-left-color: #f44336;
  background-color: #ffebee;
}

/* ===== RESPONSIVENESS ===== */
@media (max-width: 1200px) {
  .sidebar {
    width: 180px;
  }
  .main-content {
    margin-left: 180px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 60px;
    padding: 10px;
  }
  .sidebar h2 {
    font-size: 1rem;
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
  .sidebar .tab {
    padding: 8px 5px;
    font-size: 0.8rem;
  }
  .main-content {
    margin-left: 60px;
  }
  .navbar {
    padding: 0.5rem 1rem;
  }
  .user-profile .username {
    max-width: 120px;
    font-size: 0.8rem;
  }
}

/* ===== CALENDAR VIEW TYPE =====*/
#calendarContainer {
  padding: 15px 0 0 0;
  height: auto;
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  width: 100%;
}

/* Common styles for day and week grids */
.timeline-grid, .week-grid {
  display: grid;
  width: 100%;
  border: 1px solid #ddd;
  height: fit-content;
  position: relative;
  overflow-x: hidden;
}

/* Fixed grid layouts */
.timeline-grid {
  grid-template-columns: 60px 1fr;
  width: 100%;
  max-width: 100%;
}

.week-grid {
  grid-template-columns: 60px repeat(7, 1fr);
  width: 100%;
  max-width: 100%;
}

/* Common styles for headers */
.grid-header {
  height: 40px;
  padding: 5px;
  text-align: center;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  position: sticky;
  top: 0;
  z-index: 20;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-header {
  border-right: 1px solid #ddd;
}

.day-header-label.today {
  background-color: #e3f2fd;
  color: #1976d2;
}

.day-name {
  font-size: 0.8rem;
}

.day-number {
  font-size: 1rem;
}

/* Common styles for time column */
.time-column {
  background-color: #f9f9f9;
  border-right: 1px solid #ddd;
}

.hour-label {
  height: 60px;
  padding: 5px;
  text-align: right;
  border-bottom: 1px solid #ddd;
  font-size: 0.8rem;
  color: #666;
  box-sizing: border-box;
  padding-right: 8px;
}

/* Day column styles */
.day-column {
  position: relative;
  border-right: 1px solid #eee;
}

.day-column:last-child {
  border-right: none;
}

/* Hour cells */
.hours-container {
  position: relative;
}

.hour-cell {
  height: 60px;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;
}

.hour-cell-current {
  background-color: #fffde7;
}

/* Events container */
.events-container {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* Event styling for day/week view */
.events-container .event-item {
  position: absolute;
  left: 4px;
  right: 4px;
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
  border-radius: 4px;
  padding: 4px;
  font-size: 0.8rem;
  overflow: hidden;
  pointer-events: auto;
  z-index: 5;
}

/* Current time indicator */
.current-time-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e53935;
  z-index: 100;
  pointer-events: none;
}

/* Make day headers in week view clickable */
.week-grid .day-header-label {
  cursor: pointer;
  transition: background-color 0.2s;
}

.week-grid .day-header-label:hover {
  background-color: #e3f2fd;
}

/* Date navigation container */
.date-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Date display */
.date-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 200px;
}

#currentDateDisplay {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
}

/* Navigation arrows */
.nav-arrow {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.nav-arrow:hover {
  background-color: #0056b3;
}

/* Calendar view dropdown */
#calendarView {
  margin-top: 5px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 0.9rem;
}

/* Event Modal Styles */
.event-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
}

.event-modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 500px;
  max-width: 90%;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #666;
  cursor: pointer;
}

.close-modal:hover {
  color: #333;
}

.event-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.event-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.event-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-right: 25px;
}

.event-badge.low {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.event-badge.medium {
  background-color: #fff8e1;
  color: #f57c00;
}

.event-badge.high {
  background-color: #ffebee;
  color: #c62828;
}

.event-modal-body {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.event-time-info,
.event-recurrence-info,
.event-description-info {
  display: flex;
  gap: 10px;
}

.event-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  color: #666;
}

.event-time-details {
  display: flex;
  flex-direction: column;
}

#eventDate {
  font-weight: 500;
}

#eventTime {
  color: #666;
}

.event-recurrence-info {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

#eventDescription {
  line-height: 1.5;
  color: #333;
}

.event-metadata {
  margin-top: 10px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.event-genre {
  font-weight: 500;
  color: #555;
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag {
  background-color: #e0e0e0;
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.event-modal-footer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.btn-primary {
  background-color: #1a73e8;
  color: white;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn:hover {
  opacity: 0.9;
}

.event-summary-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.modal {
  display: none;
  position: fixed;
  z-index: 999;
  padding-top: 60px;
  left: 0; top: 0;
  width: 100%; height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4);
}

.modal-content {
  background-color: #fff;
  margin: auto;
  padding: 20px 30px;
  border: 1px solid #888;
  width: 60%;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}
.close:hover, .close:focus {
  color: #000;
  text-decoration: none;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 5px;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.btn:hover {
  background-color: #0056b3;
}

.btn:active {
  background-color: #004494;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-secondary:active {
  background-color: #545b62;
}

.btn-secondary:focus {
  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.5);
}

/* Notification toggle styling */
.event-notifications-control {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px 0;
  border-top: 1px solid #eee;
}

.notification-toggle-container {
  flex: 1;
}

.notification-toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-size: 0.9rem;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-switch input:checked + .toggle-slider {
  background-color: #2196F3;
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-switch input:disabled + .toggle-slider {
  background-color: #ddd;
  cursor: not-allowed;
}

.notification-toggle-label span {
  color: #666;
}

.toggle-switch input:checked ~ .notification-toggle-label span {
  color: #333;
}

/*===== TAB SWITCHING SYSTEM =====*/

.tab-content {
  display: none;
  width: 100%;
  height: 100%;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
}

/*===== NOTIFICATIONS INTEGRATION =====*/

.notifications-container {
  padding: 1.5rem;
  height: 100%;
  overflow-y: auto;
}

.notifications-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.notification-item.priority-0 {
  border-left-color: #10b981;
}

.notification-item.priority-1 {
  border-left-color: #f59e0b;
}

.notification-item.priority-2 {
  border-left-color: #ef4444;
}

.notification-item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.notification-item-time {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.notification-item-description {
  font-size: 0.95rem;
  color: #374151;
  line-height: 1.4;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 1.2rem;
}

.no-notifications {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.no-notifications i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

/*===== FIXED NAVIGATION POSITIONING =====*/

.calendar-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin: 1rem 0;
  padding: 0 1.5rem;
  position: relative;
}

.calendar-controls h2 {
  margin: 0;
  font-size: 1.5rem;
  min-width: 220px;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.calendar-controls button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 5px;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
}

.calendar-controls button:hover {
  background-color: #0056b3;
}

.calendar-controls button:first-of-type {
  position: absolute;
  left: 1.5rem;
}

.calendar-controls button:last-of-type {
  position: absolute;
  right: 1.5rem;
}
