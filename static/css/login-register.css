/*===== RESET AND BASE STYLES =====*/
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

/*===== AUTH CONTAINER STYLES =====*/
.auth-container {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.auth-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  padding: 40px;
  width: 100%;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.calendar-icon {
  font-size: 3rem;
  color: #4f46e5;
  margin-bottom: 15px;
  display: block;
  text-align: center;
}

.auth-header h1 {
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-align: center;
}

.auth-subtitle {
  color: #666;
  font-size: 1rem;
  text-align: center;
}

/* Form styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
  width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #555;
  font-size: 0.95rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 1.5px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 16px;
  padding-right: 40px;
}

/* Button styles */
.btn-primary {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 14px;
  background: #4f46e5;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
  width: 100%;
}

.btn-primary:hover {
  background: #4338ca;
  box-shadow: 0 6px 16px rgba(67, 56, 202, 0.3);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Footer styles */
.auth-footer {
  text-align: center;
  color: #666;
  font-size: 0.95rem;
  width: 100%;
}

.auth-link {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #4338ca;
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .auth-card {
    padding: 30px 20px;
  }
  
  .calendar-icon {
    font-size: 2.5rem;
  }
  
  .auth-header h1 {
    font-size: 1.5rem;
  }
}

/* Keep existing styles below */
main {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.form-card {
  background: #fff;
  padding: 2.5rem 2rem;
  max-width: 400px;
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/*===== PASSWORD ENHANCEMENT STYLES =====*/

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-container input {
  padding-right: 45px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.password-toggle:hover {
  color: #4f46e5;
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  width: 0%;
  background-color: #ef4444;
}

.strength-fill.weak {
  background-color: #ef4444;
}

.strength-fill.fair {
  background-color: #f59e0b;
}

.strength-fill.good {
  background-color: #10b981;
}

.strength-fill.strong {
  background-color: #059669;
}

.strength-text {
  font-size: 12px;
  color: #6b7280;
}

.password-match {
  margin-top: 4px;
  font-size: 12px;
  min-height: 16px;
}

.password-match.match {
  color: #10b981;
}

.password-match.no-match {
  color: #ef4444;
}
