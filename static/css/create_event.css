body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

form#eventForm {
  background-color: #ffffff;
  padding: 2rem 2.5rem;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

form#eventForm label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  display: block;
}

form#eventForm input[type="text"],
form#eventForm input[type="date"],
form#eventForm input[type="time"],
form#eventForm input[type="number"],
form#eventForm input[type="datetime-local"],
form#eventForm textarea,
form#eventForm select {
  width: 100%;
  padding: 0.6rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
  transition: border-color 0.3s;
  
  /* --- THE FIX --- */
  /* This tells all form elements to use the same font as the rest of the page. */
  font-family: inherit;
  /* --- END FIX --- */
}

form#eventForm input:focus,
form#eventForm textarea:focus,
form#eventForm select:focus {
  border-color: #1a73e8;
  outline: none;
}

form#eventForm textarea {
  resize: vertical;
  min-height: 80px;
}

form#eventForm input[type="checkbox"] {
  width: auto;
  transform: scale(1.2);
  margin-right: 0.5rem;
}

form#eventForm button[type="submit"] {
  background-color: #1a73e8;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

form#eventForm button[type="submit"]:hover {
  background-color: #1765c1;
}

@media (max-width: 600px) {
  form#eventForm {
    padding: 1.5rem;
  }
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  text-decoration: none;
  color: #1a73e8;
  font-weight: bold;
  font-size: 1rem;
  background-color: #e8f0fe;
  padding: 8px 14px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
  transition: background-color 0.3s;
  z-index: 1000;
}

.back-button:hover {
  background-color: #d2e3fc;
}

.weekday-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
}
.weekday-checkboxes label {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* --- MEGA MENU STYLES --- */
.mega-menu-container {
position: relative;
}

.tag-selector-button {
width: 100%;
padding: 0.6rem;
border: 1px solid #ccc;
border-radius: 6px;
background-color: #fff;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}

.tag-selector-button .arrow.down::after {
content: "▼";
font-size: 0.6rem;
color: #666;
}

.mega-menu-dropdown {
display: none;
position: absolute;
background-color: #fff;
border: 1px solid #ccc;
border-radius: 6px;
box-shadow: 0 4px 8px rgba(0,0,0,0.1);
width: 100%;
max-height: 300px;
overflow-y: auto;
z-index: 100;
margin-top: 5px;
}

.mega-menu-dropdown.show {
display: block;
}

.mega-menu-category {
padding: 10px;
}

.mega-menu-category h4 {
margin: 0 0 8px 0;
font-size: 0.9rem;
color: #555;
border-bottom: 1px solid #eee;
padding-bottom: 5px;
}

.mega-menu-tags {
display: flex;
flex-wrap: wrap;
gap: 8px;
}

.mega-menu-tag {
padding: 5px 10px;
border: 1px solid #ddd;
border-radius: 15px;
cursor: pointer;
transition: background-color 0.2s, color 0.2s;
font-size: 0.85rem;
}

.mega-menu-tag.selected {
background-color: #1a73e8;
color: #fff;
border-color: #1a73e8;
}

.selected-tags-display {
margin-top: 10px;
display: flex;
flex-wrap: wrap;
gap: 8px;
}

.selected-tag-pill {
background-color: #e0e0e0;
color: #333;
padding: 5px 10px;
border-radius: 15px;
font-size: 0.85rem;
display: flex;
align-items: center;
gap: 5px;
}

.selected-tag-pill .remove-tag {
cursor: pointer;
font-weight: bold;
}

/* Delete Button Styling */
.btn-danger {
  background-color: #dc3545;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  font-family: inherit;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-danger:active {
  background-color: #bd2130;
}

/* Form Actions Container for Better Layout */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.form-actions button {
  flex: 1;
  min-width: 0;
}

/* Responsive Design for Form Actions */
@media (max-width: 480px) {
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
}

\.input-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.input-group {
  flex: 1;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
}

.input-group input {
  width: 100%;
}

.info-text {
  color: #666;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  font-style: italic;
}

#recurringTimeInputs {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 1rem;
}

.input-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.input-group {
  flex: 1;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
}

.input-group input {
  width: 100%;
}

.full-width {
  width: 100%;
  margin-bottom: 15px;
}

/* Recurring Event Options Styling */
.recurring-notice {
  background-color: #e3f2fd;
  color: #1565c0;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.edit-scope-selector {
  margin-bottom: 1rem;
}

.edit-scope-selector label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
}

.edit-scope-selector select {
  width: 100%;
  padding: 0.6rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  font-family: inherit;
  background-color: #fff;
  cursor: pointer;
  transition: border-color 0.3s;
}

.edit-scope-selector select:focus {
  border-color: #1a73e8;
  outline: none;
}

#recurringEventOptions {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 1rem;
}

#recurringEventOptions hr {
  border: none;
  border-top: 1px solid #dee2e6;
  margin: 1rem 0 0 0;
}

/* ===== DELETE CONFIRMATION MODAL ===== */
.delete-modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.delete-modal-content {
  background-color: white;
  margin: 10% auto;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.delete-modal-header {
  padding: 1.5rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.delete-modal-header h2 {
  margin: 0;
  color: #dc2626;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.delete-modal-header i {
  color: #f59e0b;
}

.delete-modal-body {
  padding: 1.5rem 2rem;
}

.delete-modal-body p {
  margin: 0;
  color: #374151;
  line-height: 1.6;
  font-size: 1rem;
}

.delete-modal-footer {
  padding: 1rem 2rem 1.5rem 2rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  border-top: 1px solid #e5e7eb;
}

.delete-modal-footer button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

/* Mobile responsiveness for delete modal */
@media (max-width: 768px) {
  .delete-modal-content {
    margin: 20% auto;
    width: 95%;
  }

  .delete-modal-footer {
    flex-direction: column;
  }

  .delete-modal-footer button {
    width: 100%;
  }
}
