/* ===== BASE LAYOUT & FONT STYLES for all Profile pages ===== */
body {
  margin: 0;
  font-family: Arial, sans-serif; /* This is the default font for the main content */
  display: flex;
  height: 100vh;
  background-color: #ecf0f1;
  overflow: hidden;
}

.sidebar {
  width: 220px;
  background-color: #2c3e50;
  padding: 20px;
  box-sizing: border-box;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 100;
  display: flex;
  flex-direction: column;
  
  /* --- THE FIX --- */
  /* Explicitly set the font for the sidebar and all its children. */
  /* This guarantees consistency across all pages that use this sidebar. */
  font-family: Arial, sans-serif;
  /* --- END FIX --- */
}

.sidebar h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 1.5rem;
}

.sidebar .tab {
  display: block;
  color: #ecf0f1;
  padding: 12px 15px;
  margin-bottom: 10px;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.sidebar .tab:hover,
.sidebar .tab.active {
  background-color: #34495e;
  color: #fff;
}

.main-content {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  margin-left: 220px;
}

/* ===== PAGE-SPECIFIC STYLES ===== */
.profile-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  max-width: 700px;
  margin: 0 auto;
}

.profile-card h1 {
  text-align: center;
  margin-bottom: 1.5rem;
}

.profile-card h2 {
  text-align: left;
  margin-top: 1.5rem;
}

.profile-card p {
  text-align: left;
  color: #555;
  line-height: 1.5;
}

.profile-card .form-group {
  text-align: left;
  margin-top: 1rem;
}

.profile-card .form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
}

.save-button {
  background-color: #28a745;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 1.5rem;
  width: 100%;
}

.save-button:hover {
  background-color: #218838;
}

/* ===== MEGA MENU COMPONENT STYLES ===== */
.mega-menu-container {
  position: relative;
}

.tag-selector-button {
  width: 100%;
  padding: 0.6rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-selector-button .arrow.down::after {
  content: "▼";
  font-size: 0.6rem;
  color: #666;
}

.mega-menu-dropdown {
  display: none;
  position: absolute;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
  margin-top: 5px;
}

.mega-menu-dropdown.show {
  display: block;
}

.mega-menu-category {
  padding: 10px;
}

.mega-menu-category h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.mega-menu-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.mega-menu-tag {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 15px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  font-size: 0.85rem;
}

.mega-menu-tag.selected {
  background-color: #1a73e8;
  color: #fff;
  border-color: #1a73e8;
}

.selected-tags-display {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag-pill {
  background-color: #e0e0e0;
  color: #333;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.selected-tag-pill .remove-tag {
  cursor: pointer;
  font-weight: bold;
}
