/* ===== BASE LAYOUT & FONT STYLES for all Profile pages ===== */
body {
  margin: 0;
  font-family: Arial, sans-serif; /* This is the default font for the main content */
  display: flex;
  height: 100vh;
  background-color: #ecf0f1;
  overflow: hidden;
}

.sidebar {
  width: 220px;
  background-color: #2c3e50;
  padding: 20px;
  box-sizing: border-box;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 100;
  display: flex;
  flex-direction: column;
  
  /* --- THE FIX --- */
  /* Explicitly set the font for the sidebar and all its children. */
  /* This guarantees consistency across all pages that use this sidebar. */
  font-family: Arial, sans-serif;
  /* --- END FIX --- */
}

.sidebar h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 1.5rem;
}

.sidebar .tab {
  display: block;
  color: #ecf0f1;
  padding: 12px 15px;
  margin-bottom: 10px;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.sidebar .tab:hover,
.sidebar .tab.active {
  background-color: #34495e;
  color: #fff;
}

.main-content {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  margin-left: 220px;
}

/* ===== PAGE-SPECIFIC STYLES ===== */
.profile-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  max-width: 700px;
  margin: 0 auto;
}

.profile-card h1 {
  text-align: center;
  margin-bottom: 1.5rem;
}

.profile-card h2 {
  text-align: left;
  margin-top: 1.5rem;
}

.profile-card p {
  text-align: left;
  color: #555;
  line-height: 1.5;
}

.profile-card .form-group {
  text-align: left;
  margin-top: 1rem;
}

.profile-card .form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
}

.save-button {
  background-color: #28a745;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 1.5rem;
  width: 100%;
}

.save-button:hover {
  background-color: #218838;
}

/* ===== MEGA MENU COMPONENT STYLES ===== */
.mega-menu-container {
  position: relative;
}

.tag-selector-button {
  width: 100%;
  padding: 0.6rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-selector-button .arrow.down::after {
  content: "▼";
  font-size: 0.6rem;
  color: #666;
}

.mega-menu-dropdown {
  display: none;
  position: absolute;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
  margin-top: 5px;
}

.mega-menu-dropdown.show {
  display: block;
}

.mega-menu-category {
  padding: 10px;
}

.mega-menu-category h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.mega-menu-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.mega-menu-tag {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 15px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  font-size: 0.85rem;
}

.mega-menu-tag.selected {
  background-color: #1a73e8;
  color: #fff;
  border-color: #1a73e8;
}

.selected-tags-display {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag-pill {
  background-color: #e0e0e0;
  color: #333;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.selected-tag-pill .remove-tag {
  cursor: pointer;
  font-weight: bold;
}

/*===== NEW PROFILE LAYOUT STYLES =====*/

.profile-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.profile-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  background: #6b7280;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: #4b5563;
}

.profile-header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #1f2937;
}

.profile-content {
  flex: 1;
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.profile-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
}

/*===== ACCOUNT SETTINGS STYLES =====*/

.account-section {
  margin-bottom: 2rem;
}

.account-section h2 {
  font-size: 1.3rem;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.section-description {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 8px;
}

.avatar-display {
  position: relative;
  cursor: pointer;
}

.avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e5e7eb;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  color: white;
  font-size: 0.8rem;
}

.avatar-display:hover .avatar-overlay {
  opacity: 1;
}

.avatar-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #1a73e8;
  color: white;
}

.btn-primary:hover {
  background: #1557b0;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

/*===== SECURITY SECTION STYLES =====*/

.security-section {
  margin-bottom: 2rem;
}

.security-section h2 {
  font-size: 1.3rem;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.section-divider {
  margin: 2rem 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-group input[type="email"],
.form-group input[type="text"],
.form-group input[type="password"] {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
}

.form-group input[readonly] {
  background-color: #f9fafb;
  color: #6b7280;
}

.form-help {
  display: block;
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.25rem;
}
