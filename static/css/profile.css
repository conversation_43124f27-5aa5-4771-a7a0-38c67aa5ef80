/* ===== BASE LAYOUT & FONT STYLES for all Profile pages ===== */
body {
  margin: 0;
  font-family: Arial, sans-serif; /* This is the default font for the main content */
  display: flex;
  height: 100vh;
  background-color: #ecf0f1;
  overflow: hidden;
}

.sidebar {
  width: 220px;
  background-color: #2c3e50;
  padding: 20px;
  box-sizing: border-box;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 100;
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
}

.sidebar h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 1.5rem;
  text-align: center;
}


.sidebar .tab {
  display: block;
  color: #ecf0f1;
  padding: 12px 15px;
  margin-bottom: 10px;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.sidebar .tab:hover,
.sidebar .tab.active {
  background-color: #34495e;
  color: #fff;
}

.main-content {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  margin-left: 220px;
}

/* ===== PAGE-SPECIFIC STYLES ===== */
.profile-card form {
  max-width: 400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.profile-card form .form-group {
  width: 100%;
}

.profile-card h1 {
  text-align: center;
  margin-bottom: 1.5rem;
}

.profile-card h2 {
  text-align: left;
  margin-top: 1.5rem;
}

.profile-card p {
  text-align: left;
  color: #555;
  line-height: 1.5;
}

.profile-card .form-group {
  text-align: left;
  margin-top: 1rem;
}

.profile-card .form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
}

.save-button {
  background-color: #28a745;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 1.5rem;
  width: 100%;
}

.save-button:hover {
  background-color: #218838;
}

/* ===== MEGA MENU COMPONENT STYLES ===== */
.mega-menu-container {
  position: relative;
}

.tag-selector-button {
  width: 100%;
  padding: 0.6rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-selector-button .arrow.down::after {
  content: "▼";
  font-size: 0.6rem;
  color: #666;
}

.mega-menu-dropdown {
  display: none;
  position: absolute;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
  margin-top: 5px;
}

.mega-menu-dropdown.show {
  display: block;
}

.mega-menu-category {
  padding: 10px;
}

.mega-menu-category h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.mega-menu-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.mega-menu-tag {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 15px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  font-size: 0.85rem;
}

.mega-menu-tag.selected {
  background-color: #1a73e8;
  color: #fff;
  border-color: #1a73e8;
}

.selected-tags-display {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag-pill {
  background-color: #e0e0e0;
  color: #333;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.selected-tag-pill .remove-tag {
  cursor: pointer;
  font-weight: bold;
}

/* ===== DASHBOARD BACK BUTTON AND ICON TABS ===== */
.sidebar .tab i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.top-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 20px;
}

.back-button {
  background-color: #2980b9;
  color: white;
  padding: 10px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.back-button i {
  margin-right: 8px;
}

.back-button:hover {
  background-color: #1c6690;
}

.flash {
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-top: 1rem;
}
.flash.success {
  background-color: #e0f7e9;
  color: #2e7d32;
}
.flash.error {
  background-color: #fdecea;
  color: #c62828;
}

.info-box {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  background-color: #f9f9f9;
  display: flex;
  gap: 1rem;
  align-items: center;
  font-size: 1rem;
  margin-top: 1rem;
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

.info-box .label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.info-box .value {
  color: #555;
}

.logout-btn {
  background-color: #e53935;
  color: #fff;
  padding: 0.6rem 1.2rem;
  font-size: 0.95rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.logout-btn:hover {
  background-color: #c62828;
}

/* Container styling */
.profile-card {
  background: #fff;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.06);
  max-width: 600px;
  margin: auto;
}

/* Form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.form-group input[type="password"] {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input[type="password"]:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

/* Submit button */
.btn {
  background-color: #4285f4;
  color: white;
  padding: 0.7rem 1.4rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn:hover {
  background-color: #3367d6;
}