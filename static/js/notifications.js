document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.event-item').forEach(item => {
      item.addEventListener('click', function() {
        const eventId = this.getAttribute('data-event-id');
        fetch(`/api/event/${eventId}`)
          .then(response => response.json())
          .then(event => {
            const eventDate = new Date(event.start_time);
            navigateToDay(eventDate);
          })
          .catch(error => console.error('Error fetching event details:', error));
      });
    });
  
    function navigateToDay(date) {
      // Assuming you have a function to switch the calendar view to 'day'
      const calendarView = document.getElementById('calendarView');
      if (calendarView) {
        calendarView.value = 'day';
        // Update the current date and reload the view
        currentDate = date;
        updateDateDisplay();
        loadEventsForCurrentView();
      }
    }
  });
  