document.addEventListener('DOMContentLoaded', function() {
    const passwordForm = document.querySelector('form[action*="change-password"]');
    
    if (passwordForm) {
        passwordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(passwordForm);
            const data = {
                current_password: formData.get('current_password'),
                new_password: formData.get('new_password'),
                confirm_password: formData.get('confirm_password')
            };
            
            try {
                const response = await fetch('/profile/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    notify.success(result.message);
                    // Clear the form
                    passwordForm.reset();
                } else {
                    notify.error(result.error);
                }
            } catch (error) {
                notify.error('An error occurred while changing password.');
                console.error('Password change error:', error);
            }
        });
    }
});
