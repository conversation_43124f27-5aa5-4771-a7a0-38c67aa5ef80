/*===== PRIVACY & SECURITY FUNCTIONALITY =====*/

// Password toggle functionality
function togglePassword(fieldId) {
  const passwordField = document.getElementById(fieldId);
  const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
  
  if (passwordField.type === 'password') {
    passwordField.type = 'text';
    toggleIcon.classList.remove('fa-eye');
    toggleIcon.classList.add('fa-eye-slash');
  } else {
    passwordField.type = 'password';
    toggleIcon.classList.remove('fa-eye-slash');
    toggleIcon.classList.add('fa-eye');
  }
}

// Password strength checker
function checkPasswordStrength(password) {
  let score = 0;
  let feedback = [];

  if (password.length >= 8) score++;
  else feedback.push('at least 8 characters');

  if (/[a-z]/.test(password)) score++;
  else feedback.push('lowercase letters');

  if (/[A-Z]/.test(password)) score++;
  else feedback.push('uppercase letters');

  if (/[0-9]/.test(password)) score++;
  else feedback.push('numbers');

  if (/[^A-Za-z0-9]/.test(password)) score++;
  else feedback.push('special characters');

  return { score, feedback };
}

// Update password strength indicator
function updatePasswordStrength() {
  const password = document.getElementById('new-password').value;
  const strengthFill = document.getElementById('strength-fill');
  const strengthText = document.getElementById('strength-text');
  
  if (!password) {
    strengthFill.style.width = '0%';
    strengthFill.className = 'strength-fill';
    strengthText.textContent = 'Password strength';
    return;
  }

  const { score, feedback } = checkPasswordStrength(password);
  const percentage = (score / 5) * 100;
  
  strengthFill.style.width = percentage + '%';
  
  if (score <= 2) {
    strengthFill.className = 'strength-fill weak';
    strengthText.textContent = 'Weak - Add ' + feedback.slice(0, 2).join(', ');
  } else if (score === 3) {
    strengthFill.className = 'strength-fill fair';
    strengthText.textContent = 'Fair - Add ' + feedback.join(', ');
  } else if (score === 4) {
    strengthFill.className = 'strength-fill good';
    strengthText.textContent = 'Good - Add ' + feedback.join(', ');
  } else {
    strengthFill.className = 'strength-fill strong';
    strengthText.textContent = 'Strong password';
  }
}

// Check password match
function checkPasswordMatch() {
  const password = document.getElementById('new-password').value;
  const confirmPassword = document.getElementById('confirm-password').value;
  const matchIndicator = document.getElementById('password-match');
  
  if (!confirmPassword) {
    matchIndicator.textContent = '';
    matchIndicator.className = 'password-match';
    return false;
  }
  
  if (password === confirmPassword) {
    matchIndicator.textContent = '✓ Passwords match';
    matchIndicator.className = 'password-match match';
    return true;
  } else {
    matchIndicator.textContent = '✗ Passwords do not match';
    matchIndicator.className = 'password-match no-match';
    return false;
  }
}

document.addEventListener('DOMContentLoaded', function() {
  // Add event listeners for password strength and matching
  document.getElementById('new-password').addEventListener('input', updatePasswordStrength);
  document.getElementById('confirm-password').addEventListener('input', checkPasswordMatch);
  document.getElementById('new-password').addEventListener('input', checkPasswordMatch);

  // Handle password change form
  document.getElementById('password-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    
    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      notify.error('Please fill in all password fields.');
      return;
    }
    
    // Password strength validation
    const { score } = checkPasswordStrength(newPassword);
    if (score < 3) {
      notify.error('New password is too weak. Please choose a stronger password.');
      return;
    }
    
    // Password match validation
    if (!checkPasswordMatch()) {
      notify.error('New passwords do not match.');
      return;
    }
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Changing Password...';
    
    try {
      const response = await fetch('/api/profile/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword
        })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        notify.success('Password changed successfully!');
        // Clear form
        document.getElementById('password-form').reset();
        document.getElementById('password-strength').style.display = 'none';
        document.getElementById('password-match').textContent = '';
      } else {
        notify.error(result.error || 'Failed to change password.');
      }
    } catch (error) {
      notify.error('An error occurred while changing your password.');
    } finally {
      submitBtn.disabled = false;
      submitBtn.innerHTML = originalText;
    }
  });

  // Handle email change form
  document.getElementById('email-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const newEmail = document.getElementById('new-email').value;
    const password = document.getElementById('email-password').value;
    
    // Validation
    if (!newEmail || !password) {
      notify.error('Please fill in all fields.');
      return;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      notify.error('Please enter a valid email address.');
      return;
    }
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Changing Email...';
    
    try {
      const response = await fetch('/api/profile/change-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          new_email: newEmail,
          password: password
        })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        notify.success('Email address changed successfully!');
        // Update current email display
        document.getElementById('current-email').value = newEmail;
        // Clear form
        document.getElementById('email-form').reset();
        document.getElementById('new-email').value = '';
        document.getElementById('email-password').value = '';
      } else {
        notify.error(result.error || 'Failed to change email address.');
      }
    } catch (error) {
      notify.error('An error occurred while changing your email address.');
    } finally {
      submitBtn.disabled = false;
      submitBtn.innerHTML = originalText;
    }
  });
});
