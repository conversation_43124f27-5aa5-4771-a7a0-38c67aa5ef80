/*===== ACCOUNT SETTINGS FUNCTIONALITY =====*/

document.addEventListener('DOMContentLoaded', function() {
    const avatarUpload = document.getElementById('avatar-upload');
    const currentAvatar = document.getElementById('current-avatar');
    const removeAvatarBtn = document.getElementById('remove-avatar');
    const saveProfileBtn = document.getElementById('save-profile');
    
    let avatarChanged = false;
    let newAvatarData = null;

    // Handle avatar upload
    avatarUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                notify.error('Please select a valid image file.');
                return;
            }
            
            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                notify.error('Image file must be smaller than 5MB.');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                currentAvatar.src = e.target.result;
                newAvatarData = e.target.result;
                avatarChanged = true;
                saveProfileBtn.disabled = false;
                notify.info('Avatar preview updated. Click "Save Changes" to apply.');
            };
            reader.readAsDataURL(file);
        }
    });

    // Handle remove avatar
    removeAvatarBtn.addEventListener('click', function() {
        currentAvatar.src = '/static/images/profile-placeholder.svg';
        newAvatarData = null;
        avatarChanged = true;
        saveProfileBtn.disabled = false;
        notify.info('Avatar removed. Click "Save Changes" to apply.');
    });

    // Handle save profile
    saveProfileBtn.addEventListener('click', async function() {
        if (!avatarChanged) {
            notify.info('No changes to save.');
            return;
        }

        saveProfileBtn.disabled = true;
        saveProfileBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

        try {
            const response = await fetch('/api/profile/avatar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    avatar: newAvatarData
                })
            });

            const result = await response.json();

            if (response.ok) {
                notify.success('Profile updated successfully!');
                avatarChanged = false;
                
                // Update navbar avatar if it exists
                const navbarAvatar = document.querySelector('.navbar .avatar');
                if (navbarAvatar) {
                    navbarAvatar.src = newAvatarData || '/static/images/profile-placeholder.svg';
                }
            } else {
                notify.error(result.error || 'Failed to update profile.');
                // Revert avatar display
                currentAvatar.src = result.current_avatar || '/static/images/profile-placeholder.svg';
            }
        } catch (error) {
            notify.error('An error occurred while updating your profile.');
        } finally {
            saveProfileBtn.disabled = false;
            saveProfileBtn.innerHTML = '<i class="fas fa-save"></i> Save Changes';
        }
    });

    // Initially disable save button
    saveProfileBtn.disabled = true;
});
