"""
Locust Configuration and Test Scenarios for EventEase
"""

# Test Configuration
TEST_CONFIG = {
    'host': 'http://127.0.0.1:5001',
    'users': 50,  # Number of concurrent users
    'spawn_rate': 5,  # Users spawned per second
    'run_time': '5m',  # Test duration
}

# Performance Thresholds
PERFORMANCE_THRESHOLDS = {
    'max_response_time': 2000,  # 2 seconds max
    'max_failure_rate': 0.05,   # 5% max failure rate
    'min_rps': 10,              # Minimum requests per second
}

# Test Scenarios
TEST_SCENARIOS = {
    'light_load': {
        'users': 10,
        'spawn_rate': 2,
        'run_time': '2m',
        'description': 'Light load testing with 10 concurrent users'
    },
    'normal_load': {
        'users': 50,
        'spawn_rate': 5,
        'run_time': '5m',
        'description': 'Normal load testing with 50 concurrent users'
    },
    'stress_test': {
        'users': 100,
        'spawn_rate': 10,
        'run_time': '10m',
        'description': 'Stress testing with 100 concurrent users'
    },
    'spike_test': {
        'users': 200,
        'spawn_rate': 50,
        'run_time': '3m',
        'description': 'Spike testing with rapid user increase'
    }
}

# API Endpoints to Test
API_ENDPOINTS = {
    'authentication': [
        '/login',
        '/register',
        '/logout'
    ],
    'events': [
        '/api/events',
        '/event/create',
        '/api/event/<id>'
    ],
    'profile': [
        '/profile/info',
        '/profile/privacy',
        '/profile/preferences',
        '/api/profile/tags'
    ],
    'ai_features': [
        '/api/summarise'
    ],
    'notifications': [
        '/notifications',
        '/api/notifications'
    ]
}

# Security Test Payloads
SECURITY_PAYLOADS = {
    'xss': [
        '<script>alert("xss")</script>',
        '"><script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src=x onerror=alert("xss")>',
        '<svg onload=alert("xss")>',
        '"><img src=x onerror=alert("xss")>'
    ],
    'sql_injection': [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin'--",
        "' UNION SELECT * FROM users --",
        "1' OR '1'='1' --",
        "'; INSERT INTO users VALUES ('hacker', 'password'); --"
    ],
    'path_traversal': [
        '../../../etc/passwd',
        '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
        '....//....//....//etc/passwd',
        '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
    ],
    'command_injection': [
        '; ls -la',
        '| whoami',
        '&& cat /etc/passwd',
        '`id`',
        '$(whoami)'
    ]
}

# Expected Response Patterns
RESPONSE_PATTERNS = {
    'success_login': ['Login successful', 'dashboard'],
    'success_register': ['User created successfully'],
    'success_event_create': ['Event created successfully', 'successfully'],
    'success_profile_update': ['updated successfully'],
    'error_generic': ['Oops! Something went wrong.'],
    'error_validation': ['Invalid', 'Missing', 'required'],
    'error_auth': ['Unauthorised', 'Unauthorized', 'Invalid credentials']
}

# Load Test Profiles
LOAD_PROFILES = {
    'read_heavy': {
        'description': 'Simulates read-heavy workload (viewing events, dashboard)',
        'task_weights': {
            'view_dashboard': 5,
            'get_events': 8,
            'view_profile_pages': 3,
            'view_notifications': 2,
            'create_event': 1,
            'update_profile_tags': 1
        }
    },
    'write_heavy': {
        'description': 'Simulates write-heavy workload (creating events, updates)',
        'task_weights': {
            'view_dashboard': 2,
            'get_events': 3,
            'create_event': 5,
            'update_profile_tags': 3,
            'test_summarise_event': 2,
            'view_profile_pages': 1
        }
    },
    'balanced': {
        'description': 'Balanced read/write workload',
        'task_weights': {
            'view_dashboard': 3,
            'get_events': 5,
            'create_event': 2,
            'update_profile_tags': 1,
            'test_summarise_event': 1,
            'view_profile_pages': 1,
            'view_notifications': 1
        }
    }
}

# Database Connection Test
DB_STRESS_CONFIG = {
    'concurrent_connections': 20,
    'operations_per_connection': 100,
    'connection_timeout': 30,
    'query_timeout': 10
}

# Memory and Resource Monitoring
RESOURCE_MONITORING = {
    'memory_threshold_mb': 512,
    'cpu_threshold_percent': 80,
    'disk_io_threshold_mb': 100,
    'network_io_threshold_mb': 50
}

# Test Data Generation
TEST_DATA_CONFIG = {
    'user_count': 1000,
    'event_count': 5000,
    'tag_variations': 50,
    'date_range_days': 365
}

# Reporting Configuration
REPORT_CONFIG = {
    'output_formats': ['html', 'csv', 'json'],
    'include_charts': True,
    'include_percentiles': [50, 75, 90, 95, 99],
    'custom_metrics': [
        'authentication_success_rate',
        'event_creation_success_rate',
        'api_response_time_p95',
        'database_query_time',
        'security_test_pass_rate'
    ]
}

# Environment-specific configurations
ENVIRONMENTS = {
    'development': {
        'host': 'http://127.0.0.1:5001',
        'max_users': 50,
        'database_url': 'sqlite:///app.db'
    },
    'staging': {
        'host': 'http://staging.eventeaseapp.com',
        'max_users': 200,
        'database_url': 'postgresql://staging_db'
    },
    'production': {
        'host': 'http://eventeaseapp.com',
        'max_users': 1000,
        'database_url': 'postgresql://prod_db'
    }
}
