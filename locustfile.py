"""
Comprehensive Load Testing for EventEase Flask Application
Using Locust for performance testing and validation
"""

from locust import HttpU<PERSON>, task, between, events
import json
import random
import string
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EventEaseUser(HttpUser):
    """
    Simulates a user interacting with the EventEase application
    """
    wait_time = between(1, 3)  # Wait 1-3 seconds between tasks
    
    def on_start(self):
        """Called when a user starts - handles login"""
        self.user_data = self.create_test_user()
        self.login()
    
    def create_test_user(self):
        """Create test user data"""
        user_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return {
            'email': f'test_{user_id}@example.com',
            'password': 'TestPassword123!',
            'role': random.choice(['student', 'teacher'])
        }
    
    def login(self):
        """Login to the application"""
        # First register the user
        response = self.client.post('/register', json=self.user_data, catch_response=True)
        
        if response.status_code in [200, 409]:  # 409 if user already exists
            # Now login
            login_response = self.client.post('/login', json={
                'email': self.user_data['email'],
                'password': self.user_data['password']
            }, catch_response=True)
            
            if login_response.status_code == 200:
                logger.info(f"Successfully logged in user: {self.user_data['email']}")
                login_response.success()
            else:
                logger.error(f"Login failed for {self.user_data['email']}: {login_response.status_code}")
                login_response.failure(f"Login failed: {login_response.status_code}")
        else:
            logger.error(f"Registration failed: {response.status_code}")
    
    @task(3)
    def view_dashboard(self):
        """Test dashboard loading"""
        with self.client.get('/dashboard', catch_response=True) as response:
            if response.status_code == 200:
                if 'EventEase' in response.text or 'dashboard' in response.text:
                    response.success()
                else:
                    response.failure("Dashboard content not found")
            else:
                response.failure(f"Dashboard failed: {response.status_code}")
    
    @task(5)
    def get_events(self):
        """Test event retrieval API"""
        # Test different date ranges
        date_ranges = ['month', 'week', 'day']
        range_type = random.choice(date_ranges)
        
        start_date = datetime.now().strftime('%Y-%m-%d')
        
        with self.client.get(f'/api/events?range={range_type}&start={start_date}', 
                           catch_response=True) as response:
            if response.status_code == 200:
                try:
                    events = response.json()
                    if isinstance(events, list):
                        response.success()
                        logger.info(f"Retrieved {len(events)} events for {range_type}")
                    else:
                        response.failure("Invalid events response format")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Events API failed: {response.status_code}")
    
    @task(2)
    def create_event(self):
        """Test event creation"""
        event_data = self.generate_event_data()
        
        with self.client.post('/event/create', json=event_data, catch_response=True) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'message' in result and 'successfully' in result['message'].lower():
                        response.success()
                        logger.info(f"Created event: {event_data['title']}")
                    else:
                        response.failure("Event creation response invalid")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Event creation failed: {response.status_code}")
    
    @task(1)
    def update_profile_tags(self):
        """Test profile tags update"""
        tags = self.generate_random_tags()
        
        with self.client.post('/api/profile/tags', json={'tags': tags}, 
                            catch_response=True) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'message' in result:
                        response.success()
                    else:
                        response.failure("Profile tags response invalid")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Profile tags update failed: {response.status_code}")
    
    @task(1)
    def test_summarise_event(self):
        """Test AI event summarization"""
        event_data = {
            'title': 'Test Meeting',
            'description': 'Important meeting to discuss project progress. Please bring notebooks and laptops.',
            'start_time': '2024-01-15T10:00',
            'end_time': '2024-01-15T11:00'
        }
        
        with self.client.post('/api/summarise', json=event_data, 
                            catch_response=True) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'summary' in result or 'error' in result:
                        response.success()
                    else:
                        response.failure("Summarise response invalid")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Event summarisation failed: {response.status_code}")
    
    @task(1)
    def view_profile_pages(self):
        """Test profile page access"""
        profile_pages = ['/profile/info', '/profile/privacy', '/profile/preferences']
        page = random.choice(profile_pages)
        
        with self.client.get(page, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Profile page {page} failed: {response.status_code}")
    
    @task(1)
    def view_notifications(self):
        """Test notifications page"""
        with self.client.get('/notifications', catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Notifications failed: {response.status_code}")
    
    def generate_event_data(self):
        """Generate random event data for testing"""
        titles = [
            'Team Meeting', 'Project Review', 'Client Call', 'Training Session',
            'Workshop', 'Conference', 'Presentation', 'Planning Session'
        ]
        
        descriptions = [
            'Important meeting to discuss progress',
            'Review of current project status',
            'Client presentation and feedback',
            'Training on new technologies'
        ]
        
        start_time = datetime.now() + timedelta(days=random.randint(1, 30))
        end_time = start_time + timedelta(hours=random.randint(1, 4))
        
        return {
            'title': random.choice(titles),
            'description': random.choice(descriptions),
            'start_time': start_time.strftime('%Y-%m-%dT%H:%M'),
            'end_time': end_time.strftime('%Y-%m-%dT%H:%M'),
            'priority': random.randint(0, 3),
            'tags': self.generate_random_tags() if self.user_data['role'] == 'teacher' else '',
            'event_type': 'single'
        }
    
    def generate_random_tags(self):
        """Generate random tags for testing"""
        tag_options = ['meeting', 'important', 'project', 'client', 'training', 'review']
        num_tags = random.randint(1, 3)
        selected_tags = random.sample(tag_options, num_tags)
        return ', '.join(selected_tags)

class SecurityTestUser(HttpUser):
    """
    Specialized user for testing security features and input validation
    """
    wait_time = between(0.5, 1.5)
    weight = 1  # Lower weight than normal users
    
    @task(1)
    def test_xss_attempts(self):
        """Test XSS prevention"""
        xss_payloads = [
            '<script>alert("xss")</script>',
            '"><script>alert("xss")</script>',
            'javascript:alert("xss")',
            '<img src=x onerror=alert("xss")>'
        ]
        
        payload = random.choice(xss_payloads)
        
        # Test in event creation
        event_data = {
            'title': payload,
            'description': f'Test description {payload}',
            'start_time': '2024-01-15T10:00',
            'end_time': '2024-01-15T11:00',
            'priority': 1,
            'event_type': 'single'
        }
        
        with self.client.post('/event/create', json=event_data, catch_response=True) as response:
            # Should not return 500 error - input should be sanitized
            if response.status_code in [200, 400, 401, 403]:
                response.success()
            else:
                response.failure(f"XSS test failed with: {response.status_code}")
    
    @task(1)
    def test_sql_injection_attempts(self):
        """Test SQL injection prevention"""
        sql_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users --"
        ]
        
        payload = random.choice(sql_payloads)
        
        # Test in login
        with self.client.post('/login', json={
            'email': f'{payload}@test.com',
            'password': 'password123'
        }, catch_response=True) as response:
            # Should not return 500 error - should handle gracefully
            if response.status_code in [400, 401]:
                response.success()
            else:
                response.failure(f"SQL injection test failed: {response.status_code}")

# Event handlers for custom metrics
@events.request.add_listener
def my_request_handler(request_type, name, response_time, response_length, response, context, exception, **kwargs):
    """Custom request handler for additional metrics"""
    if exception:
        logger.error(f"Request failed: {name} - {exception}")

@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Called when test starts"""
    logger.info("🚀 Starting EventEase load testing...")
    logger.info(f"Target host: {environment.host}")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Called when test stops"""
    logger.info("🏁 EventEase load testing completed!")
    
    # Print summary statistics
    stats = environment.stats
    logger.info(f"Total requests: {stats.total.num_requests}")
    logger.info(f"Total failures: {stats.total.num_failures}")
    logger.info(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    logger.info(f"Max response time: {stats.total.max_response_time:.2f}ms")
    logger.info(f"Requests per second: {stats.total.current_rps:.2f}")

if __name__ == "__main__":
    # This allows running the file directly for testing
    import os
    os.system("locust -f locustfile.py --host=http://127.0.0.1:5001")
