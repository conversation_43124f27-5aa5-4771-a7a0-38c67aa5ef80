<!DOCTYPE html>
<html>
<head>
    <title>Test Notifications</title>
    <link rel="stylesheet" href="/static/css/notification-system.css" />
</head>
<body>
    <h1>Test Notifications</h1>
    <button onclick="testSuccess()">Test Success</button>
    <button onclick="testError()">Test Error</button>
    <button onclick="testPreferences()">Test Preferences Save</button>
    
    <script src="/static/js/notification-system.js"></script>
    <script>
        function testSuccess() {
            notify.success('This is a success message!');
        }
        
        function testError() {
            notify.error('This is an error message!');
        }
        
        function testPreferences() {
            notify.success('Your tags have been updated successfully!');
        }
        
        console.log('Notification system loaded:', typeof notify);
    </script>
</body>
</html>
