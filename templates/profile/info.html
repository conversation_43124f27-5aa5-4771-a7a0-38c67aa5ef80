<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Profile</title>
  <link rel="stylesheet" href="/static/css/profile.css" />
</head>

<body>
<!-- sidebar.html (or included in all pages) -->
  <div class="sidebar">
    <h2>Profile Menu</h2>
    <a href="/profile/info" class="tab">User Info</a>
    <a href="/profile/account" class="tab">Account Settings</a>
    <a href="/profile/privacy" class="tab">Privacy & Security</a>
    <a href="/profile/preferences" class="tab">Preferences</a>
  </div>
  <div class="main-content" > <!-- the main content thing is what isolates the background-->
    <div class="profile-container">
      <div class="profile-card">
        <div class="profile-info">
          <h2>User Email</h2>
          <p class="email">{{ user.email }}</p>

          <div class="profile-actions">
            <form action="/logout" method="POST">
              <button class="logout-btn">Logout</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="temp-dashboard-button">
    <button id="dashboardBtn">Back to Dashboard</button>
  </div>  
  
  <script src="/static/js/profile.js"></script>
</body>
</html>
