<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Account Settings - School Calendar</title>
  <link rel="stylesheet" href="/static/css/profile.css" />
  <link rel="stylesheet" href="/static/css/notification-system.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
  <div class="profile-container">
    <div class="profile-header">
      <button class="back-button" onclick="window.location.href='/dashboard'">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
      </button>
      <h1>Account Settings</h1>
    </div>

    <div class="profile-content">
      <div class="profile-card">
        <div class="account-section">
          <h2>Profile Information</h2>
          <p class="section-description">Manage your avatar and display information.</p>

          <div class="avatar-section">
            <div class="avatar-display">
              <img id="current-avatar" src="{{ user_avatar or '/static/images/profile-placeholder.svg' }}" alt="User Avatar" class="avatar-large" />
              <div class="avatar-overlay">
                <i class="fas fa-camera"></i>
                <span>Change Photo</span>
              </div>
            </div>

            <div class="avatar-controls">
              <input type="file" id="avatar-upload" accept="image/*" style="display: none;">
              <button class="btn-primary" onclick="document.getElementById('avatar-upload').click()">
                <i class="fas fa-upload"></i> Upload New Avatar
              </button>
              <button class="btn-secondary" id="remove-avatar">
                <i class="fas fa-trash"></i> Remove Avatar
              </button>
            </div>
          </div>

          <div class="form-group">
            <label for="display-email">Email Address</label>
            <input type="email" id="display-email" value="{{ user_email }}" readonly>
            <small class="form-help">Your email address is displayed in the profile menu.</small>
          </div>

          <div class="form-group">
            <label for="display-role">Account Role</label>
            <input type="text" id="display-role" value="{{ user_role|title }}" readonly>
            <small class="form-help">Your account role determines your permissions.</small>
          </div>

          <button class="btn-primary" id="save-profile">
            <i class="fas fa-save"></i> Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>

  <script src="/static/js/notification-system.js"></script>
  <script src="/static/js/account.js"></script>
</body>
</html>
