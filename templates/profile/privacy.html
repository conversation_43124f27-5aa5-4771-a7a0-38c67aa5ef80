<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Privacy & Security - School Calendar</title>
  <link rel="stylesheet" href="/static/css/profile.css" />
  <link rel="stylesheet" href="/static/css/notification-system.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
  <div class="profile-container">
    <div class="profile-header">
      <button class="back-button" onclick="window.location.href='/dashboard'">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
      </button>
      <h1>Privacy & Security</h1>
    </div>

    <div class="profile-content">
      <div class="profile-card">
        <div class="security-section">
          <h2>Change Password</h2>
          <p class="section-description">Update your password to keep your account secure.</p>

          <form id="password-form">
            <div class="form-group">
              <label for="current-password">Current Password</label>
              <div class="password-input-container">
                <input type="password" id="current-password" name="current-password" required>
                <button type="button" class="password-toggle" onclick="togglePassword('current-password')">
                  <i class="fas fa-eye" id="current-password-toggle-icon"></i>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label for="new-password">New Password</label>
              <div class="password-input-container">
                <input type="password" id="new-password" name="new-password" required>
                <button type="button" class="password-toggle" onclick="togglePassword('new-password')">
                  <i class="fas fa-eye" id="new-password-toggle-icon"></i>
                </button>
              </div>
              <div class="password-strength" id="password-strength">
                <div class="strength-bar">
                  <div class="strength-fill" id="strength-fill"></div>
                </div>
                <span class="strength-text" id="strength-text">Password strength</span>
              </div>
            </div>

            <div class="form-group">
              <label for="confirm-password">Confirm New Password</label>
              <div class="password-input-container">
                <input type="password" id="confirm-password" name="confirm-password" required>
                <button type="button" class="password-toggle" onclick="togglePassword('confirm-password')">
                  <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                </button>
              </div>
              <div class="password-match" id="password-match"></div>
            </div>

            <button type="submit" class="btn-primary">
              <i class="fas fa-key"></i> Change Password
            </button>
          </form>
        </div>

        <hr class="section-divider">

        <div class="security-section">
          <h2>Change Email Address</h2>
          <p class="section-description">Update your email address for login and notifications.</p>

          <form id="email-form">
            <div class="form-group">
              <label for="current-email">Current Email</label>
              <input type="email" id="current-email" value="{{ user_email }}" readonly>
            </div>

            <div class="form-group">
              <label for="new-email">New Email Address</label>
              <input type="email" id="new-email" name="new-email" required>
            </div>

            <div class="form-group">
              <label for="email-password">Confirm Password</label>
              <div class="password-input-container">
                <input type="password" id="email-password" name="password" required>
                <button type="button" class="password-toggle" onclick="togglePassword('email-password')">
                  <i class="fas fa-eye" id="email-password-toggle-icon"></i>
                </button>
              </div>
            </div>

            <button type="submit" class="btn-primary">
              <i class="fas fa-envelope"></i> Change Email
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script src="/static/js/notification-system.js"></script>
  <script src="/static/js/privacy.js"></script>
</body>
</html>
