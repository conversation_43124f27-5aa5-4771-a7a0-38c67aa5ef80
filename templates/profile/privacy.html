<!-- templates/profile/privacy.html -->
{% extends "profile/profile_base.html" %}
{% block title %}Privacy & Security{% endblock %}
{% set active_tab = 'privacy' %}

{% block content %}
  <div class="profile-card">
    <h1>Privacy & Security</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}

      {% endif %}
    {% endwith %}
  

    <form method="POST" action="{{ url_for('change_password') }}">
      <div class="form-group">
        <label for="current_password">Current Password</label>
        <input type="password" name="current_password" required>
      </div>

      <div class="form-group">
        <label for="new_password">New Password</label>
        <input type="password" name="new_password" required>
      </div>

      <div class="form-group">
        <label for="confirm_password">Confirm New Password</label>
        <input type="password" name="confirm_password" required>
      </div>

      <button type="submit" class="btn">Change Password</button>
    </form>
  </div>
{% endblock %}

{% block scripts %}
  <link rel="stylesheet" href="/static/css/notification-system.css" />
  <script src="/static/js/notification-system.js"></script>
  <script src="/static/js/privacy.js"></script>
{% endblock %}
