<!-- templates/profile/privacy.html -->
{% extends "profile/profile_base.html" %}
{% block title %}Privacy & Security{% endblock %}
{% set active_tab = 'privacy' %}

{% block content %}
  <div class="profile-card">
    <h1>Privacy & Security</h1>
    <p>Manage your privacy settings and password below.</p>

    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <ul class="flashes">
          {% for category, message in messages %}
            <li class="flash {{ category }}">{{ message }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    {% endwith %}

    <form method="POST" action="{{ url_for('change_password') }}">
      <div class="form-group">
        <label for="current_password">Current Password</label>
        <input type="password" name="current_password" required>
      </div>

      <div class="form-group">
        <label for="new_password">New Password</label>
        <input type="password" name="new_password" required>
      </div>

      <div class="form-group">
        <label for="confirm_password">Confirm New Password</label>
        <input type="password" name="confirm_password" required>
      </div>

      <button type="submit" class="btn">Change Password</button>
    </form>
  </div>
{% endblock %}
