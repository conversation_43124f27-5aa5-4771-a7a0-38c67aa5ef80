<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EventEase - Login</title>
  <!-- Authentication page styles -->
  <link rel="stylesheet" href="/static/css/login-register.css" />
  <!-- Notification system for user feedback -->
  <link rel="stylesheet" href="/static/css/notification-system.css" />
  <!-- Font Awesome icons for UI elements -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
</head>
<body>
  <!-- Main authentication container -->
  <div class="auth-container">
    <div class="auth-card">
      <!-- Application branding and title -->
      <div class="auth-header">
        <i class="fas fa-calendar-alt calendar-icon"></i>
        <h1>EventEase</h1>
        <p class="auth-subtitle">Sign in to your account</p>
      </div>
      
      <form class="auth-form">

        <!-- <input type="hidden" name="csrf_token" value="{{ csrf_token }}"> CSRF token passed thru POST method  -->

        <div class="form-group">
          <label for="email"><i class="fas fa-envelope"></i> Email</label>
          <input type="email" id="email" name="email" required placeholder="<EMAIL>">
        </div>
        
        <div class="form-group">
          <label for="password"><i class="fas fa-lock"></i> Password</label>
          <div class="password-input-container">
            <input type="password" id="password" name="password" required placeholder="••••••••">
            <button type="button" class="password-toggle" onclick="togglePassword('password')">
              <i class="fas fa-eye" id="password-toggle-icon"></i>
            </button>
          </div>
        </div>
        
        <button type="button" id = "LgnBtn" class="btn-primary">
          <i class="fas fa-sign-in-alt"></i> Login
        </button>
      </form>
      
      <div class="auth-footer">
        <p>Don't have an account? <a href="/register" class="auth-link">Register here</a></p>
      </div>
    </div>
  </div>

  <script src="/static/js/notification-system.js"></script>
  <script src="/static/js/login.js"></script>
  
</body>
</html>
