<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Edit Event</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <link rel="stylesheet" href="/static/css/create_event.css" />
  <link rel="stylesheet" href="/static/css/notification-system.css" />
</head>
<body data-user-role="{{ user_role }}">
  <a href="/dashboard" class="back-button">← Back to Dashboard</a>

  <form id="eventForm">
    <!-- For single-day events -->
    <div id="singleDayInputs" style="display: none;">
      <div class="input-group full-width">
        <label for="event_date">Date:</label>
        <input type="date" id="event_date">
      </div>
      
      <div class="input-row">
        <div class="input-group">
          <label for="single_start_time">Start Time:</label>
          <input type="time" id="single_start_time">
        </div>
        <div class="input-group">
          <label for="single_end_time">End Time:</label>
          <input type="time" id="single_end_time">
        </div>
      </div>
    </div>
    
    <!-- For multi-day events -->
    <div id="multiDayInputs" style="display: none;">
      <div class="input-row">
        <div class="input-group">
          <label for="multi_start_date">Start Date:</label>
          <input type="date" id="multi_start_date">
        </div>
        <div class="input-group">
          <label for="multi_start_time">Start Time:</label>
          <input type="time" id="multi_start_time">
        </div>
      </div>
      
      <div class="input-row">
        <div class="input-group">
          <label for="multi_end_date">End Date:</label>
          <input type="date" id="multi_end_date">
        </div>
        <div class="input-group">
          <label for="multi_end_time">End Time:</label>
          <input type="time" id="multi_end_time">
        </div>
      </div>
    </div>
    
    <!-- For recurring events, only show time inputs -->
    <div id="recurringTimeInputs" style="display: none;">
      <div class="input-row">
        <div class="input-group">
          <label for="rec_start_time">Start Time:</label>
          <input type="time" id="rec_start_time">
        </div>
        <div class="input-group">
          <label for="rec_end_time">End Time:</label>
          <input type="time" id="rec_end_time">
        </div>
      </div>
      <p class="info-text">Note: For recurring events, you can only change the time, not the date.</p>
    </div>

    <div id="recurringEventOptions" style="display: none;">
      <div class="recurring-notice">
        <i class="fas fa-repeat"></i> This is a recurring event.
      </div>
      
      <div class="edit-scope-selector">
        <label for="edit-scope-dropdown">What would you like to edit?</label>
        <select id="edit-scope-dropdown" name="edit-scope">
          <option value="this">Only this occurrence</option>
          <option value="all">All occurrences in the series</option>
          <option value="future">This and all future occurrences</option>
        </select>
      </div>
      <hr>
    </div>
    
    <br><hr><br>

    <label>Title:</label>
    <input type="text" id="title" required><br>

    <label>Description:</label>
    <textarea id="description"></textarea><br>

    <label>Priority:</label>
    <select id="priority">
      <option value="0">Low</option>
      <option value="1">Medium</option>
      <option value="2">High</option>
    </select><br>

    <div id="tag-section">
      <div class="form-group">
        <label>Tags</label>
        <div class="mega-menu-container">
          <div id="tag-selector-button" class="tag-selector-button">
            <span>Select Tags...</span>
          </div>
          <div id="mega-menu-dropdown" class="mega-menu-dropdown"></div>
          <input type="hidden" name="tags" id="selected-tags-input">
        </div>
        <div id="selected-tags-display" class="selected-tags-display"></div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <button type="submit" id="submitBtn">Save Changes</button>
      <button type="button" id="deleteEventBtn" class="btn-danger">Delete Event</button>
    </div>
  </form>

  <script src="/static/js/notification-system.js"></script>
  <script src="/static/js/edit_event.js"></script>
</body>
</html>
