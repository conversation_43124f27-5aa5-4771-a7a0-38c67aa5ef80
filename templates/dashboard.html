<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Dashboard</title>
  <link rel="stylesheet" href="/static/css/dashboard.css" />
  <link rel="stylesheet" href="/static/css/notification-system.css" />
  <link rel="stylesheet" href="/static/css/create_event.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

</head>
<body data-user-id="{{ user_id }}" data-user-role="{{ user_role }}">
  <div class="sidebar">
    <h2>Menu</h2>
    <a class="tab active" id="dashboard-tab" href="#" onclick="switchTab('dashboard')">
      <i class="fas fa-calendar-alt"></i> Dashboard
    </a>
    <a class="tab" id="notifications-tab" href="#" onclick="switchTab('notifications')">
      <i class="fas fa-bell"></i> Notifications
    </a>
    <a class="tab" id="preferences-tab" href="#" onclick="switchTab('preferences')">
      <i class="fas fa-cog"></i> Preferences
    </a>
  </div>

  <div class="main-content">
    <div class="navbar">
      <div class="nav-left">
        <button class="create-button"><i class="fas fa-plus"></i> Create</button>
      </div>
      
      <div class="nav-center">
        <div class="date-navigation">
          <button id="prevMonth" class="nav-arrow">←</button>
          
          <div class="date-display">
            <h2 id="currentDateDisplay"></h2>
            <select id="calendarView">
              <option value="month">Month</option>
              <option value="week">Week</option>
              <option value="day">Day</option>
            </select>
          </div>
          
          <button id="nextMonth" class="nav-arrow">→</button>
        </div>
      </div>
      
      <div class="user-menu">
        <div class="user-profile" onclick="document.querySelector('.menu-dropdown').classList.toggle('show')">
          <img src="/static/images/profile-placeholder.svg" alt="User Avatar" class="avatar" />
          <span class="username">{{ user_email or 'Guest' }}</span>
          <i class="arrow down"></i>
        </div>
      
        <div class="menu-dropdown">
          <a href="/profile/account">Profile Settings</a>
          <a href="/profile/privacy">Privacy & Security</a>
          <form action="/logout" method="POST" class="placeholder">
            <button id="logout">Logout</button>
          </form>
        </div>
      </div>
    </div>
    
    <!-- Dashboard Content -->
    <div id="dashboard-content" class="tab-content active">
      <div class="calendar-container">
        <div class="calendar-grid">
        </div>
      </div>
      <div id="calendarContainer" style="display: none;"></div> <!-- for week/day views only -->
    </div>

    <!-- Notifications Content -->
    <div id="notifications-content" class="tab-content">
      <div class="notifications-container">
        <h1 class="notifications-title">Upcoming Event Notifications</h1>
        <div id="notifications-list" class="notifications-list">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading notifications...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Preferences Content -->
    <div id="preferences-content" class="tab-content">
      <div class="preferences-container">
        <div class="preferences-card">
          <h1 class="preferences-title">Preferences</h1>
          <p class="preferences-subtitle">Manage your notification settings and customize your calendar experience by selecting tags relevant to you.</p>

          <hr class="preferences-divider">

          <h2 class="preferences-section-title">My Calendar Tags</h2>
          <p class="preferences-section-description">Select tags that apply to you. You will see events on your calendar that match these tags.</p>

          <!-- This div holds the user's current tags, for JavaScript to read -->
          <div id="user-current-tags" data-current-tags="{{ current_tags }}"></div>

          <div class="form-group">
            <label class="preferences-label">Your Tags</label>
            <div class="mega-menu-container">
              <div id="tag-selector-button" class="tag-selector-button">
                <span>Select Your Tags...</span>
                <i class="arrow down"></i>
              </div>
              <div id="mega-menu-dropdown" class="mega-menu-dropdown">
                <!-- Populated by JavaScript -->
              </div>
              <input type="hidden" id="selected-tags-input">
            </div>
            <div id="selected-tags-display" class="selected-tags-display"></div>
          </div>

          <button id="saveTagsBtn" class="save-button">Save My Tags</button>
        </div>
      </div>
    </div>

  </div>

  <div id="eventModal" class="event-modal">
    <div class="event-modal-content">
      <span class="close-modal">×</span>
      <div class="event-modal-header">
        <h2 id="eventTitle">Event Title</h2>
        <div class="event-badge" id="eventPriority">Medium</div>
      </div>
      <div class="event-modal-body">
        <div class="event-time-info">
          <div id="eventTimeIcon" class="event-icon"><i class="fas fa-clock"></i></div>
          <div id="eventTimeDetails">
            <div id="eventDate">July 15, 2025</div>
            <div id="eventTime">9:00 AM - 10:30 AM</div>
          </div>
        </div>
        
        <div class="event-recurrence-info" id="eventRecurrenceInfo">
          <div class="event-icon"><i class="fas fa-repeat"></i></div>
          <div id="eventRecurrence">Repeats every week</div>
        </div>
        
        <div class="event-description-info">
          <div class="event-icon"><i class="fas fa-align-left"></i></div>
          <div id="eventDescription">Event description goes here...</div>
        </div>
        
        <div class="event-metadata">
          <div class="event-tags" id="eventTags">
          </div>
        </div>

        <div class="event-notifications-control">
          <div class="event-icon"><i class="fas fa-bell"></i></div>
          <div class="notification-toggle-container">
            <label class="notification-toggle-label">
              <span id="notificationToggleText">Notifications enabled</span>
              <div class="toggle-switch">
                <input type="checkbox" id="notificationToggle" checked>
                <span class="toggle-slider"></span>
              </div>
            </label>
          </div>
        </div>
      </div>

      <div id="summaryModal" class="modal">
        <div class="modal-content">
          <span class="close" onclick="closeSummaryModal()">&times;</span>
          <h2>Summary of Your Events</h2>
          <div id="summaryResult" class="event-summary-info">
            Loading summary...
          </div>
        </div>
      </div>

      <div class="event-modal-footer">
        <button id="summariseBtn" class="btn btn-secondary" data-event-id="123">Summarise</button>
        <button id="editEventBtn" class="btn btn-primary">Edit</button>
      </div>
    </div>
  </div>

  <script src="/static/js/notification-system.js"></script>
  <script src="/static/js/preferences.js"></script>
  <script src="/static/js/dashboard.js"></script>
</body>
</html>
