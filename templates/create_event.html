<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Create Event</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <link rel="stylesheet" href="/static/css/create_event.css" />
  <link rel="stylesheet" href="/static/css/notification-system.css" />
</head>
<!-- Pass the user's role from the backend to a data attribute -->
<body data-user-role="{{ user_role }}">
  <a href="/dashboard" class="back-button">← Back to Dashboard</a>

  <form id="eventForm">
    <label for="event_type">Event Type:</label>
    <select id="event_type" name="event_type" required>
      <option value="single">Single-Day Event</option>
      <option value="multi">Multi-Day Event</option>
      <option value="recurring">Recurring Event</option>
    </select>

    <p>
      Creating event for <strong id="displayDate">{{date}}</strong>
    </p>
    
    <div id="singleDayInputs">
      <label for="event_date_picker">Event Date:</label>
      <input type="date" id="event_date_picker" name="event_date_picker">
      <input type="hidden" id="event_date" name="event_date">

      <label for="start_time">Start Time:</label>
      <input type="time" id="start_time" value="09:00">
      
      <label for="end_time">End Time:</label>
      <input type="time" id="end_time" value="10:00">
    </div>

    <div id="multiDayInputs" style="display: none;">
      <label>Start Date:</label>
      <input type="date" id="multi_start_date">
      <label>Start Time:</label>
      <input type="time" id="multi_start_time">
      <label>End Date:</label>
      <input type="date" id="multi_end_date">
      <label>End Time:</label>
      <input type="time" id="multi_end_time">
    </div>

    <div id="recurringDayInputs" style="display: none;">
      <label>Repeat Unit:</label>
      <select id="rec_unit">
        <option value="daily">Daily</option>
        <option value="weekly">Weekly</option>
        <option value="monthly">Monthly</option>
      </select>
      <label>Interval (every X units):</label>
      <input type="number" id="rec_interval" min="1" value="1">
      <label for="start_time_rec">Start Time:</label>
      <input type="time" id="start_time_rec" value="09:00">
      <label for="end_time_rec">End Time:</label>
      <input type="time" id="end_time_rec" value="10:00">
      <label>Start Date:</label>
      <input type="date" id="rec_start_date">
      <label>Ends Date:</label>
      <input type="date" id="rec_ends">
      <div id="weekdaySelector" style="display: none; margin-top: 10px;">
        <label>Weekdays:</label>
        <div class="weekday-checkboxes">
          <label><input type="checkbox" value="MO"> Mon</label>
          <label><input type="checkbox" value="TU"> Tue</label>
          <label><input type="checkbox" value="WE"> Wed</label>
          <label><input type="checkbox" value="TH"> Thu</label>
          <label><input type="checkbox" value="FR"> Fri</label>
          <label><input type="checkbox" value="SA"> Sat</label>
          <label><input type="checkbox" value="SU"> Sun</label>
        </div>
      </div>
    </div>
    
    <br><hr><br>

    <label>Title:</label>
    <input type="text" id="title" required><br>

    <label>Description:</label>
    <textarea id="description"></textarea><br>

    <label>Priority:</label>
    <select id="priority">
      <option value="0">Low</option>
      <option value="1">Medium</option>
      <option value="2">High</option>
    </select><br>

    <!-- This whole section will be hidden for students by the JS -->
    <div id="tag-section">
        <div class="form-group">
            <label>Tags</label>
            <div class="mega-menu-container">
                <div id="tag-selector-button" class="tag-selector-button">
                    <span>Select Tags...</span>
                    <i class="arrow down"></i>
                </div>
                <div id="mega-menu-dropdown" class="mega-menu-dropdown">
                    <!-- This will be populated by JavaScript -->
                </div>
                <input type="hidden" name="tags" id="selected-tags-input">
            </div>
            <div id="selected-tags-display" class="selected-tags-display"></div>
        </div>
    </div>

    <button type="submit" id="submitBtn">Create Event</button>
  </form>

  <script src="/static/js/notification-system.js"></script>
  <script src="/static/js/create_event.js"></script>
</body>
</html>
