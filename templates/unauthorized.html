<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Access Denied - School Calendar</title>
  <link rel="stylesheet" href="/static/css/styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    /*===== UNAUTHORIZED PAGE STYLES =====*/
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      text-align: center;
      padding: 2rem;
    }
    
    .error-icon {
      font-size: 6rem;
      color: #ef4444;
      margin-bottom: 2rem;
    }
    
    .error-title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 1rem;
    }
    
    .error-message {
      font-size: 1.1rem;
      color: #6b7280;
      margin-bottom: 2rem;
      max-width: 500px;
      line-height: 1.6;
    }
    
    .error-actions {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .btn-error {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background-color: #4f46e5;
      color: white;
      text-decoration: none;
      border-radius: 0.5rem;
      font-weight: 500;
      transition: background-color 0.2s;
    }
    
    .btn-error:hover {
      background-color: #4338ca;
    }
    
    .btn-error.secondary {
      background-color: #6b7280;
    }
    
    .btn-error.secondary:hover {
      background-color: #4b5563;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <i class="fas fa-shield-alt error-icon"></i>
    <h1 class="error-title">Access Denied</h1>
    <p class="error-message">
      You don't have permission to access this page. Please log in with an authorized account or contact your administrator if you believe this is an error.
    </p>
    <div class="error-actions">
      <a href="/login" class="btn-error">
        <i class="fas fa-sign-in-alt"></i>
        Login
      </a>
      <a href="/register" class="btn-error secondary">
        <i class="fas fa-user-plus"></i>
        Register
      </a>
    </div>
  </div>
</body>
</html>
