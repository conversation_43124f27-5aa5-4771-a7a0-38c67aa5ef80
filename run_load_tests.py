#!/usr/bin/env python3
"""
EventEase Load Testing Runner
Comprehensive load testing with multiple scenarios and detailed reporting
"""

import os
import sys
import subprocess
import time
import json
import argparse
from datetime import datetime
from locust_config import TEST_SCENARIOS, PERFORMANCE_THRESHOLDS, TEST_CONFIG

class LoadTestRunner:
    def __init__(self, host='http://127.0.0.1:5001'):
        self.host = host
        self.results_dir = f"load_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.results_dir, exist_ok=True)
        
    def run_scenario(self, scenario_name, scenario_config):
        """Run a specific load test scenario"""
        print(f"\n🚀 Running {scenario_name}: {scenario_config['description']}")
        print(f"   Users: {scenario_config['users']}")
        print(f"   Spawn Rate: {scenario_config['spawn_rate']}/sec")
        print(f"   Duration: {scenario_config['run_time']}")
        
        # Prepare output files
        html_report = os.path.join(self.results_dir, f"{scenario_name}_report.html")
        csv_stats = os.path.join(self.results_dir, f"{scenario_name}_stats.csv")
        csv_failures = os.path.join(self.results_dir, f"{scenario_name}_failures.csv")
        
        # Build locust command using python module
        cmd = [
            'python3', '-m', 'locust',
            '-f', 'locustfile.py',
            '--host', self.host,
            '--users', str(scenario_config['users']),
            '--spawn-rate', str(scenario_config['spawn_rate']),
            '--run-time', scenario_config['run_time'],
            '--headless',
            '--html', html_report,
            '--csv', os.path.join(self.results_dir, scenario_name),
            '--print-stats',
            '--only-summary'
        ]
        
        print(f"   Command: {' '.join(cmd)}")
        
        try:
            # Run the test
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            end_time = time.time()
            
            # Parse results
            test_results = self.parse_results(result, start_time, end_time)
            
            # Save detailed results
            results_file = os.path.join(self.results_dir, f"{scenario_name}_results.json")
            with open(results_file, 'w') as f:
                json.dump(test_results, f, indent=2)
            
            # Print summary
            self.print_summary(scenario_name, test_results)
            
            return test_results
            
        except subprocess.TimeoutExpired:
            print(f"❌ {scenario_name} timed out!")
            return {'status': 'timeout', 'scenario': scenario_name}
        except Exception as e:
            print(f"❌ {scenario_name} failed: {e}")
            return {'status': 'error', 'scenario': scenario_name, 'error': str(e)}
    
    def parse_results(self, result, start_time, end_time):
        """Parse locust output and extract metrics"""
        output_lines = result.stdout.split('\n')
        
        test_results = {
            'status': 'completed' if result.returncode == 0 else 'failed',
            'duration': end_time - start_time,
            'return_code': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'metrics': {}
        }
        
        # Parse key metrics from output
        for line in output_lines:
            if 'Total requests' in line:
                test_results['metrics']['total_requests'] = self.extract_number(line)
            elif 'Total failures' in line:
                test_results['metrics']['total_failures'] = self.extract_number(line)
            elif 'Average response time' in line:
                test_results['metrics']['avg_response_time'] = self.extract_float(line)
            elif 'Max response time' in line:
                test_results['metrics']['max_response_time'] = self.extract_float(line)
            elif 'Requests/s' in line or 'RPS' in line:
                test_results['metrics']['requests_per_second'] = self.extract_float(line)
        
        # Calculate derived metrics
        if 'total_requests' in test_results['metrics'] and 'total_failures' in test_results['metrics']:
            total_req = test_results['metrics']['total_requests']
            total_fail = test_results['metrics']['total_failures']
            if total_req > 0:
                test_results['metrics']['failure_rate'] = total_fail / total_req
                test_results['metrics']['success_rate'] = 1 - (total_fail / total_req)
        
        return test_results
    
    def extract_number(self, line):
        """Extract integer from line"""
        import re
        numbers = re.findall(r'\d+', line)
        return int(numbers[0]) if numbers else 0
    
    def extract_float(self, line):
        """Extract float from line"""
        import re
        numbers = re.findall(r'\d+\.?\d*', line)
        return float(numbers[0]) if numbers else 0.0
    
    def print_summary(self, scenario_name, results):
        """Print test summary"""
        print(f"\n📊 {scenario_name} Results:")
        print(f"   Status: {'✅ PASSED' if results['status'] == 'completed' else '❌ FAILED'}")
        
        if 'metrics' in results:
            metrics = results['metrics']
            print(f"   Total Requests: {metrics.get('total_requests', 'N/A')}")
            print(f"   Total Failures: {metrics.get('total_failures', 'N/A')}")
            print(f"   Success Rate: {metrics.get('success_rate', 0)*100:.1f}%")
            print(f"   Avg Response Time: {metrics.get('avg_response_time', 'N/A')}ms")
            print(f"   Max Response Time: {metrics.get('max_response_time', 'N/A')}ms")
            print(f"   Requests/Second: {metrics.get('requests_per_second', 'N/A')}")
            
            # Check against thresholds
            self.check_thresholds(scenario_name, metrics)
    
    def check_thresholds(self, scenario_name, metrics):
        """Check results against performance thresholds"""
        print(f"\n🎯 Performance Threshold Check for {scenario_name}:")
        
        # Check max response time
        max_rt = metrics.get('max_response_time', 0)
        if max_rt <= PERFORMANCE_THRESHOLDS['max_response_time']:
            print(f"   ✅ Max Response Time: {max_rt}ms (threshold: {PERFORMANCE_THRESHOLDS['max_response_time']}ms)")
        else:
            print(f"   ❌ Max Response Time: {max_rt}ms exceeds threshold of {PERFORMANCE_THRESHOLDS['max_response_time']}ms")
        
        # Check failure rate
        failure_rate = metrics.get('failure_rate', 0)
        if failure_rate <= PERFORMANCE_THRESHOLDS['max_failure_rate']:
            print(f"   ✅ Failure Rate: {failure_rate*100:.1f}% (threshold: {PERFORMANCE_THRESHOLDS['max_failure_rate']*100}%)")
        else:
            print(f"   ❌ Failure Rate: {failure_rate*100:.1f}% exceeds threshold of {PERFORMANCE_THRESHOLDS['max_failure_rate']*100}%")
        
        # Check RPS
        rps = metrics.get('requests_per_second', 0)
        if rps >= PERFORMANCE_THRESHOLDS['min_rps']:
            print(f"   ✅ Requests/Second: {rps} (minimum: {PERFORMANCE_THRESHOLDS['min_rps']})")
        else:
            print(f"   ❌ Requests/Second: {rps} below minimum of {PERFORMANCE_THRESHOLDS['min_rps']}")
    
    def run_all_scenarios(self):
        """Run all test scenarios"""
        print("🧪 Starting EventEase Load Testing Suite")
        print(f"Target Host: {self.host}")
        print(f"Results Directory: {self.results_dir}")
        
        all_results = {}
        
        for scenario_name, scenario_config in TEST_SCENARIOS.items():
            results = self.run_scenario(scenario_name, scenario_config)
            all_results[scenario_name] = results
            
            # Brief pause between scenarios
            time.sleep(5)
        
        # Generate final report
        self.generate_final_report(all_results)
        
        return all_results
    
    def generate_final_report(self, all_results):
        """Generate comprehensive final report"""
        report_file = os.path.join(self.results_dir, "final_report.json")
        
        final_report = {
            'test_run_info': {
                'timestamp': datetime.now().isoformat(),
                'host': self.host,
                'total_scenarios': len(all_results)
            },
            'scenarios': all_results,
            'summary': self.calculate_summary(all_results)
        }
        
        with open(report_file, 'w') as f:
            json.dump(final_report, f, indent=2)
        
        # Print final summary
        self.print_final_summary(final_report)
        
        print(f"\n📁 All results saved to: {self.results_dir}")
        print(f"📄 Final report: {report_file}")
    
    def calculate_summary(self, all_results):
        """Calculate overall summary statistics"""
        completed_tests = [r for r in all_results.values() if r.get('status') == 'completed']
        
        if not completed_tests:
            return {'status': 'no_completed_tests'}
        
        total_requests = sum(r.get('metrics', {}).get('total_requests', 0) for r in completed_tests)
        total_failures = sum(r.get('metrics', {}).get('total_failures', 0) for r in completed_tests)
        
        return {
            'completed_scenarios': len(completed_tests),
            'failed_scenarios': len(all_results) - len(completed_tests),
            'total_requests': total_requests,
            'total_failures': total_failures,
            'overall_success_rate': (total_requests - total_failures) / total_requests if total_requests > 0 else 0,
            'avg_response_time': sum(r.get('metrics', {}).get('avg_response_time', 0) for r in completed_tests) / len(completed_tests),
            'max_response_time': max(r.get('metrics', {}).get('max_response_time', 0) for r in completed_tests)
        }
    
    def print_final_summary(self, final_report):
        """Print final test summary"""
        summary = final_report['summary']
        
        print("\n" + "="*60)
        print("🏁 FINAL LOAD TESTING SUMMARY")
        print("="*60)
        print(f"Completed Scenarios: {summary.get('completed_scenarios', 0)}")
        print(f"Failed Scenarios: {summary.get('failed_scenarios', 0)}")
        print(f"Total Requests: {summary.get('total_requests', 0):,}")
        print(f"Total Failures: {summary.get('total_failures', 0):,}")
        print(f"Overall Success Rate: {summary.get('overall_success_rate', 0)*100:.1f}%")
        print(f"Average Response Time: {summary.get('avg_response_time', 0):.1f}ms")
        print(f"Max Response Time: {summary.get('max_response_time', 0):.1f}ms")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description='EventEase Load Testing Runner')
    parser.add_argument('--host', default='http://127.0.0.1:5001', help='Target host URL')
    parser.add_argument('--scenario', choices=list(TEST_SCENARIOS.keys()), help='Run specific scenario only')
    
    args = parser.parse_args()
    
    runner = LoadTestRunner(args.host)
    
    if args.scenario:
        # Run single scenario
        scenario_config = TEST_SCENARIOS[args.scenario]
        results = runner.run_scenario(args.scenario, scenario_config)
    else:
        # Run all scenarios
        results = runner.run_all_scenarios()
    
    return results

if __name__ == "__main__":
    main()
