{"test_config": {"host": "http://127.0.0.1:5001", "users": 5, "duration": 30, "timestamp": "20250722_013603"}, "results": [{"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 7.80797004699707, "success": true, "timestamp": "2025-07-22T01:35:32.179665"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 3.3109188079833984, "success": true, "timestamp": "2025-07-22T01:35:33.538273"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 8.440017700195312, "success": true, "timestamp": "2025-07-22T01:35:34.514455"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 6.386041641235352, "success": true, "timestamp": "2025-07-22T01:35:36.517088"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 3.1499862670898438, "success": true, "timestamp": "2025-07-22T01:35:37.865625"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 5.362749099731445, "success": true, "timestamp": "2025-07-22T01:35:39.550275"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 2.373218536376953, "success": true, "timestamp": "2025-07-22T01:35:41.272099"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 5.791902542114258, "success": true, "timestamp": "2025-07-22T01:35:42.835380"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 4.084110260009766, "success": true, "timestamp": "2025-07-22T01:35:44.683355"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 7.606029510498047, "success": true, "timestamp": "2025-07-22T01:35:45.487039"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 8.513927459716797, "success": true, "timestamp": "2025-07-22T01:35:46.036994"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 11.455059051513672, "success": true, "timestamp": "2025-07-22T01:35:47.270069"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 5.328893661499023, "success": true, "timestamp": "2025-07-22T01:35:48.257962"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 4.497051239013672, "success": true, "timestamp": "2025-07-22T01:35:49.286402"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 3.968954086303711, "success": true, "timestamp": "2025-07-22T01:35:50.262800"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 4.004716873168945, "success": true, "timestamp": "2025-07-22T01:35:51.481163"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 7.688045501708984, "success": true, "timestamp": "2025-07-22T01:35:52.080793"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 4.315853118896484, "success": true, "timestamp": "2025-07-22T01:35:53.660451"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 6.345033645629883, "success": true, "timestamp": "2025-07-22T01:35:54.906113"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 5.12385368347168, "success": true, "timestamp": "2025-07-22T01:35:55.902795"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 8.22591781616211, "success": true, "timestamp": "2025-07-22T01:35:57.552401"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 5.835056304931641, "success": true, "timestamp": "2025-07-22T01:35:58.106998"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 9.961128234863281, "success": true, "timestamp": "2025-07-22T01:35:59.658150"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 2.1789073944091797, "success": true, "timestamp": "2025-07-22T01:36:00.252433"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 6.456136703491211, "success": true, "timestamp": "2025-07-22T01:35:32.169531"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 2.9230117797851562, "success": true, "timestamp": "2025-07-22T01:35:33.560184"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 2.748250961303711, "success": true, "timestamp": "2025-07-22T01:35:34.679494"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 3.056049346923828, "success": true, "timestamp": "2025-07-22T01:35:35.810108"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 6.953954696655273, "success": true, "timestamp": "2025-07-22T01:35:36.630319"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 5.115985870361328, "success": true, "timestamp": "2025-07-22T01:35:38.454559"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 5.812168121337891, "success": true, "timestamp": "2025-07-22T01:35:39.222431"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 2.1610260009765625, "success": true, "timestamp": "2025-07-22T01:35:40.174449"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 9.173154830932617, "success": true, "timestamp": "2025-07-22T01:35:40.942956"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 4.0988922119140625, "success": true, "timestamp": "2025-07-22T01:35:42.595741"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 2.753019332885742, "success": true, "timestamp": "2025-07-22T01:35:43.809771"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 2.332925796508789, "success": true, "timestamp": "2025-07-22T01:35:45.184680"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 10.906219482421875, "success": true, "timestamp": "2025-07-22T01:35:46.707351"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 2.5730133056640625, "success": true, "timestamp": "2025-07-22T01:35:47.877636"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 2.3660659790039062, "success": true, "timestamp": "2025-07-22T01:35:48.998575"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 6.796836853027344, "success": true, "timestamp": "2025-07-22T01:35:49.615051"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 3.7131309509277344, "success": true, "timestamp": "2025-07-22T01:35:51.244535"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 8.094072341918945, "success": true, "timestamp": "2025-07-22T01:35:52.261470"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 5.830049514770508, "success": true, "timestamp": "2025-07-22T01:35:53.319827"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 8.625984191894531, "success": true, "timestamp": "2025-07-22T01:35:55.258892"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 8.143186569213867, "success": true, "timestamp": "2025-07-22T01:35:56.165274"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 5.623102188110352, "success": true, "timestamp": "2025-07-22T01:35:56.728039"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 7.26008415222168, "success": true, "timestamp": "2025-07-22T01:35:58.449850"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 5.254983901977539, "success": true, "timestamp": "2025-07-22T01:35:59.685834"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 4.333019256591797, "success": true, "timestamp": "2025-07-22T01:36:01.308600"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 3.187894821166992, "success": true, "timestamp": "2025-07-22T01:35:32.172505"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 3.6563873291015625, "success": true, "timestamp": "2025-07-22T01:35:33.794268"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 6.294012069702148, "success": true, "timestamp": "2025-07-22T01:35:34.907136"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 6.531238555908203, "success": true, "timestamp": "2025-07-22T01:35:36.355695"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 4.709720611572266, "success": true, "timestamp": "2025-07-22T01:35:37.850494"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 3.2858848571777344, "success": true, "timestamp": "2025-07-22T01:35:39.227689"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 10.740280151367188, "success": true, "timestamp": "2025-07-22T01:35:40.811357"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 8.83793830871582, "success": true, "timestamp": "2025-07-22T01:35:42.699955"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 2.435922622680664, "success": true, "timestamp": "2025-07-22T01:35:43.643155"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 64.96095657348633, "success": true, "timestamp": "2025-07-22T01:35:45.078020"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 5.070209503173828, "success": true, "timestamp": "2025-07-22T01:35:46.733351"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 9.485006332397461, "success": true, "timestamp": "2025-07-22T01:35:48.566972"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 3.983020782470703, "success": true, "timestamp": "2025-07-22T01:35:49.228241"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 9.690046310424805, "success": true, "timestamp": "2025-07-22T01:35:50.454852"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 2.527952194213867, "success": true, "timestamp": "2025-07-22T01:35:52.331108"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 4.594326019287109, "success": true, "timestamp": "2025-07-22T01:35:53.241541"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 6.53529167175293, "success": true, "timestamp": "2025-07-22T01:35:54.379045"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 3.337860107421875, "success": true, "timestamp": "2025-07-22T01:35:55.647618"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 3.615856170654297, "success": true, "timestamp": "2025-07-22T01:35:56.777547"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 6.613969802856445, "success": true, "timestamp": "2025-07-22T01:35:58.782586"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 4.45103645324707, "success": true, "timestamp": "2025-07-22T01:36:00.700672"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 4.673957824707031, "success": true, "timestamp": "2025-07-22T01:35:32.184285"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 3.607034683227539, "success": true, "timestamp": "2025-07-22T01:35:33.388015"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 12.443304061889648, "success": true, "timestamp": "2025-07-22T01:35:34.212560"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 5.176067352294922, "success": true, "timestamp": "2025-07-22T01:35:36.082902"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 8.679866790771484, "success": true, "timestamp": "2025-07-22T01:35:37.151610"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 3.677845001220703, "success": true, "timestamp": "2025-07-22T01:35:39.066213"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 2.681255340576172, "success": true, "timestamp": "2025-07-22T01:35:40.151161"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 6.859064102172852, "success": true, "timestamp": "2025-07-22T01:35:40.960237"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 2.4840831756591797, "success": true, "timestamp": "2025-07-22T01:35:42.359804"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 5.691051483154297, "success": true, "timestamp": "2025-07-22T01:35:44.086289"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 7.629871368408203, "success": true, "timestamp": "2025-07-22T01:35:45.547756"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 11.526823043823242, "success": true, "timestamp": "2025-07-22T01:35:47.469770"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 7.727146148681641, "success": true, "timestamp": "2025-07-22T01:35:48.419506"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 3.6079883575439453, "success": true, "timestamp": "2025-07-22T01:35:50.226728"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 5.959987640380859, "success": true, "timestamp": "2025-07-22T01:35:51.663764"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 6.636142730712891, "success": true, "timestamp": "2025-07-22T01:35:53.074988"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 9.505748748779297, "success": true, "timestamp": "2025-07-22T01:35:54.356619"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 4.860877990722656, "success": true, "timestamp": "2025-07-22T01:35:55.151123"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 9.571075439453125, "success": true, "timestamp": "2025-07-22T01:35:56.662332"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 9.095191955566406, "success": true, "timestamp": "2025-07-22T01:35:57.489853"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 3.8781166076660156, "success": true, "timestamp": "2025-07-22T01:35:59.194031"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 2.8269290924072266, "success": true, "timestamp": "2025-07-22T01:36:00.073927"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 5.542755126953125, "success": true, "timestamp": "2025-07-22T01:36:00.628726"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 6.063222885131836, "success": true, "timestamp": "2025-07-22T01:36:01.592194"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 4.149913787841797, "success": true, "timestamp": "2025-07-22T01:35:32.179971"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 6.602048873901367, "success": true, "timestamp": "2025-07-22T01:35:33.865118"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 6.767034530639648, "success": true, "timestamp": "2025-07-22T01:35:34.978459"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 5.879878997802734, "success": true, "timestamp": "2025-07-22T01:35:36.403532"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 2.8700828552246094, "success": true, "timestamp": "2025-07-22T01:35:38.189853"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 3.3359527587890625, "success": true, "timestamp": "2025-07-22T01:35:40.149278"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 7.76982307434082, "success": true, "timestamp": "2025-07-22T01:35:41.590442"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 4.162073135375977, "success": true, "timestamp": "2025-07-22T01:35:42.709077"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 18.67079734802246, "success": true, "timestamp": "2025-07-22T01:35:43.949843"}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "response_time": 2.5670528411865234, "success": true, "timestamp": "2025-07-22T01:35:45.864247"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 3.1201839447021484, "success": true, "timestamp": "2025-07-22T01:35:47.484322"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 4.855155944824219, "success": true, "timestamp": "2025-07-22T01:35:49.177076"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 8.687257766723633, "success": true, "timestamp": "2025-07-22T01:35:49.706779"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 3.644227981567383, "success": true, "timestamp": "2025-07-22T01:35:51.244591"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 6.667852401733398, "success": true, "timestamp": "2025-07-22T01:35:51.773373"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 11.33108139038086, "success": true, "timestamp": "2025-07-22T01:35:52.996892"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 4.034996032714844, "success": true, "timestamp": "2025-07-22T01:35:54.764460"}, {"endpoint": "/api/events", "method": "GET", "status_code": 200, "response_time": 12.890100479125977, "success": true, "timestamp": "2025-07-22T01:35:56.570826"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 8.654117584228516, "success": true, "timestamp": "2025-07-22T01:35:57.656668"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 4.071950912475586, "success": true, "timestamp": "2025-07-22T01:35:58.196163"}, {"endpoint": "/api/profile/tags", "method": "POST", "status_code": 200, "response_time": 7.967948913574219, "success": true, "timestamp": "2025-07-22T01:35:59.868808"}, {"endpoint": "/profile/info", "method": "GET", "status_code": 200, "response_time": 5.795717239379883, "success": true, "timestamp": "2025-07-22T01:36:00.397877"}, {"endpoint": "/event/create", "method": "POST", "status_code": 200, "response_time": 11.229991912841797, "success": true, "timestamp": "2025-07-22T01:36:01.737239"}]}