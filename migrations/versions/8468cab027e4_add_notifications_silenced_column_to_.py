"""Add notifications_silenced column to events

Revision ID: 8468cab027e4
Revises: 
Create Date: 2025-07-20 01:51:02.353818

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8468cab027e4'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('events', schema=None) as batch_op:
        batch_op.add_column(sa.Column('notifications_silenced', sa.<PERSON>(), nullable=False, server_default='0'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('events', schema=None) as batch_op:
        batch_op.drop_column('notifications_silenced')

    # ### end Alembic commands ###
