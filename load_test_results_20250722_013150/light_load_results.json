{"status": "completed", "duration": 120.29152202606201, "return_code": 0, "stdout": "", "stderr": "[2025-07-22 01:31:50,960] cuz-3/INFO/locust.main: Starting Locust 2.37.14\n[2025-07-22 01:31:50,963] cuz-3/INFO/locust.main: Run time limit set to 120 seconds\n[2025-07-22 01:31:50,963] cuz-3/INFO/locustfile: 🚀 Starting EventEase load testing...\n[2025-07-22 01:31:50,963] cuz-3/INFO/locustfile: Target host: http://127.0.0.1:5001\n[2025-07-22 01:31:50,963] cuz-3/INFO/locust.runners: Ramping to 10 users at a rate of 2.00 per second\n[2025-07-22 01:31:51,114] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:51,115] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:51Z <Greenlet at 0x105546660: run_user(<locustfile.EventEaseUser object at 0x1050f4cb0>)> failed with LocustError\n\n[2025-07-22 01:31:52,116] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:52,116] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:52Z <Greenlet at 0x105577560: run_user(<locustfile.EventEaseUser object at 0x10552c0e0>)> failed with LocustError\n\n[2025-07-22 01:31:52,117] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:52,117] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:52Z <Greenlet at 0x105577380: run_user(<locustfile.EventEaseUser object at 0x1055427b0>)> failed with LocustError\n\n[2025-07-22 01:31:53,118] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:53,118] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:53Z <Greenlet at 0x1055b80e0: run_user(<locustfile.EventEaseUser object at 0x1055ad790>)> failed with LocustError\n\n[2025-07-22 01:31:53,121] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:53,121] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:53Z <Greenlet at 0x1055b8360: run_user(<locustfile.EventEaseUser object at 0x1055ade50>)> failed with LocustError\n\n[2025-07-22 01:31:53,123] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:53,123] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:53Z <Greenlet at 0x1055b8720: run_user(<locustfile.EventEaseUser object at 0x1055ae0c0>)> failed with LocustError\n\n[2025-07-22 01:31:54,125] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:54,125] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:54Z <Greenlet at 0x1055b9da0: run_user(<locustfile.EventEaseUser object at 0x1055afe60>)> failed with LocustError\n\n[2025-07-22 01:31:54,131] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:54,131] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:54Z <Greenlet at 0x1055b9b20: run_user(<locustfile.EventEaseUser object at 0x1055cabd0>)> failed with LocustError\n\n[2025-07-22 01:31:54,139] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:54,139] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:54Z <Greenlet at 0x1055ba020: run_user(<locustfile.EventEaseUser object at 0x1055cac00>)> failed with LocustError\n\n[2025-07-22 01:31:54,140] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:54,140] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:54Z <Greenlet at 0x1055ba340: run_user(<locustfile.EventEaseUser object at 0x1055cad20>)> failed with LocustError\n\n[2025-07-22 01:31:54,984] cuz-3/INFO/locust.runners: All users spawned: {\"EventEaseUser\": 5, \"SecurityTestUser\": 5} (10 total users)\n[2025-07-22 01:31:55,164] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:55,164] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:55Z <Greenlet at 0x1055bbce0: run_user(<locustfile.EventEaseUser object at 0x1055cadb0>)> failed with LocustError\n\n[2025-07-22 01:31:55,169] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:55,170] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:55Z <Greenlet at 0x10561c7c0: run_user(<locustfile.EventEaseUser object at 0x10560a390>)> failed with LocustError\n\n[2025-07-22 01:31:55,171] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:55,171] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:55Z <Greenlet at 0x10561c4a0: run_user(<locustfile.EventEaseUser object at 0x1055af890>)> failed with LocustError\n\n[2025-07-22 01:31:55,179] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:55,179] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:55Z <Greenlet at 0x10561c0e0: run_user(<locustfile.EventEaseUser object at 0x1055e4e30>)> failed with LocustError\n\n[2025-07-22 01:31:55,181] cuz-3/INFO/locustfile: Successfully logged in user: <EMAIL>\n[2025-07-22 01:31:55,181] cuz-3/ERROR/locust.user.users: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\nTraceback (most recent call last):\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n\nTraceback (most recent call last):\n  File \"src/gevent/greenlet.py\", line 900, in gevent._gevent_cgreenlet.Greenlet.run\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 187, in run_user\n    user.run()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/user/users.py\", line 149, in run\n    self.on_start()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 26, in on_start\n    self.login()\n  File \"/Users/<USER>/williamAT3/locustfile.py\", line 51, in login\n    login_response.success()\n  File \"/Users/<USER>/Library/Python/3.12/lib/python/site-packages/locust/clients.py\", line 405, in success\n    raise LocustError(\nlocust.exception.LocustError: Tried to set status on a request that has not yet been made. Make sure you use a with-block, like this:\n\nwith self.client.request(..., catch_response=True) as response:\n    response.success()\n2025-07-21T15:31:55Z <Greenlet at 0x1055bb9c0: run_user(<locustfile.EventEaseUser object at 0x1055e78c0>)> failed with LocustError\n\n[2025-07-22 01:33:50,708] cuz-3/INFO/locust.main: --run-time limit reached, shutting down\n[2025-07-22 01:33:50,728] cuz-3/INFO/locustfile: 🏁 EventEase load testing completed!\n[2025-07-22 01:33:50,728] cuz-3/INFO/locustfile: Total requests: 593\n[2025-07-22 01:33:50,728] cuz-3/INFO/locustfile: Total failures: 0\n[2025-07-22 01:33:50,728] cuz-3/INFO/locustfile: Average response time: 2.30ms\n[2025-07-22 01:33:50,728] cuz-3/INFO/locustfile: Max response time: 35.45ms\n[2025-07-22 01:33:50,728] cuz-3/INFO/locustfile: Requests per second: 4.90\n[2025-07-22 01:33:50,754] cuz-3/INFO/locust.main: writing html report to file: load_test_results_20250722_013150/light_load_report.html\n[2025-07-22 01:33:50,755] cuz-3/INFO/locust.main: Shutting down (exit code 0)\nType     Name                                                                          # reqs      # fails |    Avg     Min     Max    Med |   req/s  failures/s\n--------|----------------------------------------------------------------------------|-------|-------------|-------|-------|-------|-------|--------|-----------\nPOST     /event/create                                                                    304     0(0.00%) |      2       0      35      2 |    2.54        0.00\nPOST     /login                                                                           289     0(0.00%) |      2       1      35      2 |    2.42        0.00\n--------|----------------------------------------------------------------------------|-------|-------------|-------|-------|-------|-------|--------|-----------\n         Aggregated                                                                       593     0(0.00%) |      2       0      35      2 |    4.96        0.00\n\nResponse time percentiles (approximated)\nType     Name                                                                                  50%    66%    75%    80%    90%    95%    98%    99%  99.9% 99.99%   100% # reqs\n--------|--------------------------------------------------------------------------------|--------|------|------|------|------|------|------|------|------|------|------|------\nPOST     /event/create                                                                           2      2      2      2      3      4      6      8     35     35     35    304\nPOST     /login                                                                                  2      2      2      3      4      6     13     17     35     35     35    289\n--------|--------------------------------------------------------------------------------|--------|------|------|------|------|------|------|------|------|------|------|------\n         Aggregated                                                                              2      2      2      2      4      5      7     16     35     35     35    593\n\n", "metrics": {}}