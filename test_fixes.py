#!/usr/bin/env python3

import requests
import json

BASE_URL = "http://127.0.0.1:5001"

def test_fixes():
    """Test the calendar width and profile menu fixes"""
    session = requests.Session()
    
    print("Testing Calendar Width and Profile Menu Fixes...")
    print("=" * 60)
    
    # Login as teacher
    print("1. Logging in as teacher...")
    login_data = {
        'email': '<EMAIL>',
        'password': 'teacher'
    }
    
    response = session.post(f"{BASE_URL}/login", json=login_data)
    if response.status_code != 200:
        print(f"   ✗ Login failed: {response.text}")
        return False
    print("   ✓ Login successful")
    
    # Test dashboard layout
    print("2. Testing dashboard layout...")
    response = session.get(f"{BASE_URL}/dashboard")
    if response.status_code == 200:
        content = response.text
        # Check that the tab-content structure is present
        if 'dashboard-content' in content and 'tab-content' in content:
            print("   ✓ Dashboard tab structure present")
        else:
            print("   ✗ Dashboard tab structure missing")
            return False
        
        # Check that calendar container is present
        if 'calendar-container' in content:
            print("   ✓ Calendar container present")
        else:
            print("   ✗ Calendar container missing")
            return False
    else:
        print(f"   ✗ Dashboard failed: {response.text}")
        return False
    
    # Test profile menu navigation
    print("3. Testing profile menu navigation...")
    response = session.get(f"{BASE_URL}/profile/account")
    if response.status_code == 200:
        content = response.text
        if 'Account Settings' in content and 'avatar-upload' in content:
            print("   ✓ Profile account page accessible and functional")
        else:
            print("   ✗ Profile account page missing expected content")
            return False
    else:
        print(f"   ✗ Profile account page failed: {response.text}")
        return False
    
    # Test navbar content
    print("4. Testing navbar profile menu...")
    response = session.get(f"{BASE_URL}/dashboard")
    if response.status_code == 200:
        content = response.text
        if 'Profile Settings' in content and '/profile/account' in content:
            print("   ✓ Navbar has correct 'Profile Settings' link")
        else:
            print("   ✗ Navbar missing 'Profile Settings' link")
            return False
    else:
        print(f"   ✗ Dashboard failed: {response.text}")
        return False
    
    print("\n" + "=" * 60)
    print("✅ All fixes verified successfully!")
    print("\n📋 Fixed Issues:")
    print("   ✅ Calendar width restored to full width (sidebar to screen edge)")
    print("   ✅ Profile menu 'Profile Settings' now links to account page")
    print("   ✅ Calendar cell overflow handling preserved")
    print("   ✅ Tab switching functionality maintained")
    return True

if __name__ == "__main__":
    try:
        success = test_fixes()
        if success:
            print("\n🎉 Both issues have been successfully fixed!")
        else:
            print("\n❌ Some issues remain. Please check the output above.")
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
